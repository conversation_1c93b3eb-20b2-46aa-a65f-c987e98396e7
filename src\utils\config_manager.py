#!/usr/bin/env python3
"""
Configuration Manager for GA Modbus Python Application
Handles reading and writing TOML configuration files
"""

import os
import tomli
import tomli_w
from typing import Dict, Any, Optional
from .logger import get_logger, log_exception, log_function_entry, log_function_exit


class ConfigManager:
    """Manages application configuration in TOML format"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.logger = get_logger(__name__ + '.ConfigManager')
        
        if config_path is None:
            # Default to config.toml in the project root (two levels up from src/utils)
            script_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            config_path = os.path.join(script_dir, 'config.toml')
        
        self.config_path = config_path
        self.config = self._load_default_config()
        self.load_config()
        
    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration"""
        return {
            'output_path': 'log',
            'modbus': {
                'port': 'COM3',
                'baudrate': 9600,
                'parity': 'E',
                'stopbits': 1,
                'bytesize': 8,
                'slave_id': 1,
                'query_interval': 0.5
            },
            'gui': {
                'last_serial_port': '',
                'last_baudrate': 9600,
                'last_slave_id': 1,
                'csv_logging_enabled': True,
                'theme': 'light',
                'update_interval': 1.0
            }
        }
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from TOML file"""
        log_function_entry(self.logger, "load_config", config_path=self.config_path)
        
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'rb') as f:
                    loaded_config = tomli.load(f)
                
                # Merge with defaults to ensure all keys exist
                self._merge_config(loaded_config)
                self.logger.info(f"Configuration loaded from {self.config_path}")
            else:
                self.logger.info(f"Config file not found: {self.config_path}, using defaults")
                # Create the config file with defaults
                self.save_config()
        except Exception as e:
            self.logger.error(f"Error loading config: {e}")
            log_exception(self.logger, "Config loading error")
            # Continue with default config
        
        # Convert relative paths to absolute
        if not os.path.isabs(self.config['output_path']):
            project_root = os.path.dirname(self.config_path)
            self.config['output_path'] = os.path.join(project_root, self.config['output_path'])
        
        log_function_exit(self.logger, "load_config", True)
        return self.config
    
    def _merge_config(self, loaded_config: Dict[str, Any]):
        """Merge loaded config with defaults to ensure all keys exist"""
        def merge_dict(default: Dict, loaded: Dict) -> Dict:
            result = default.copy()
            for key, value in loaded.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = merge_dict(result[key], value)
                else:
                    result[key] = value
            return result
        
        self.config = merge_dict(self.config, loaded_config)
    
    def save_config(self):
        """Save current configuration to TOML file"""
        log_function_entry(self.logger, "save_config", config_path=self.config_path)
        
        try:
            # Make sure directory exists
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            # Convert absolute output_path back to relative for saving
            config_to_save = self.config.copy()
            project_root = os.path.dirname(self.config_path)
            try:
                rel_path = os.path.relpath(self.config['output_path'], project_root)
                config_to_save['output_path'] = rel_path
            except ValueError:
                # Keep absolute path if relpath fails
                pass
            
            with open(self.config_path, 'wb') as f:
                tomli_w.dump(config_to_save, f)
            
            self.logger.info(f"Configuration saved to {self.config_path}")
            log_function_exit(self.logger, "save_config", True)
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
            log_exception(self.logger, "Config saving error")
            log_function_exit(self.logger, "save_config", False)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key (supports dot notation)"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
    
    def set(self, key: str, value: Any, save: bool = True):
        """Set configuration value by key (supports dot notation)"""
        log_function_entry(self.logger, "set", key=key, value=value, save=save)
        
        keys = key.split('.')
        config_ref = self.config
        
        # Navigate to the parent of the target key
        for k in keys[:-1]:
            if k not in config_ref:
                config_ref[k] = {}
            config_ref = config_ref[k]
        
        # Set the value
        config_ref[keys[-1]] = value
        
        if save:
            self.save_config()
        
        self.logger.debug(f"Set config {key} = {value}")
        log_function_exit(self.logger, "set")
    
    def get_modbus_config(self) -> Dict[str, Any]:
        """Get modbus configuration section"""
        return self.config.get('modbus', {})
    
    def get_gui_config(self) -> Dict[str, Any]:
        """Get GUI configuration section"""
        return self.config.get('gui', {})
    
    def set_gui_config(self, gui_config: Dict[str, Any], save: bool = True):
        """Update GUI configuration section"""
        if 'gui' not in self.config:
            self.config['gui'] = {}
        
        self.config['gui'].update(gui_config)
        
        if save:
            self.save_config()
    
    def update_last_connection(self, port: str, baudrate: int, slave_id: int, save: bool = True):
        """Update last used connection parameters"""
        log_function_entry(self.logger, "update_last_connection", 
                          port=port, baudrate=baudrate, slave_id=slave_id)
        
        gui_config = {
            'last_serial_port': port,
            'last_baudrate': baudrate,
            'last_slave_id': slave_id
        }
        
        self.set_gui_config(gui_config, save)
        self.logger.info(f"Updated last connection: {port} @ {baudrate} baud, slave {slave_id}")
        log_function_exit(self.logger, "update_last_connection")
    
    def get_last_connection(self) -> Dict[str, Any]:
        """Get last used connection parameters"""
        gui_config = self.get_gui_config()
        return {
            'port': gui_config.get('last_serial_port', ''),
            'baudrate': gui_config.get('last_baudrate', 9600),
            'slave_id': gui_config.get('last_slave_id', 1)
        }


# Global instance for easy access
_config_manager = None

def get_config_manager() -> ConfigManager:
    """Get the global config manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager