"""
Session Manager for GA Modbus Python Application

Manages monitoring session lifecycle, auto-start logging, and session data persistence.
Integrates with QSettings for persistent storage and provides both manual and automatic
session creation based on current flow thresholds.
"""

import uuid
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from PyQt6.QtCore import QSettings, QObject, pyqtSignal

# Import logging
from ..utils.logger import get_logger, log_exception, log_function_entry, log_function_exit


class Session:
    """Data model for a monitoring session"""
    
    def __init__(self):
        # Core session metadata
        self.session_id: str = str(uuid.uuid4())
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.duration_seconds: int = 0
        self.status: str = 'pending'  # 'pending', 'active', 'completed', 'interrupted'
        
        # Trigger information
        self.trigger_type: str = 'manual'  # 'manual', 'auto_charge', 'auto_discharge'
        self.auto_start_threshold: Optional[float] = None
        self.auto_end_enabled: bool = False
        self.recovery_phase: bool = False
        
        # Data association
        self.csv_files: List[str] = []
        self.connection_params: Dict[str, Any] = {}
        
        # Session statistics
        self.data_summary: Dict[str, Any] = {
            'total_records': 0,
            'data_start_time': None,
            'data_end_time': None,
            'avg_cell_voltage': 0.0,
            'avg_pack_voltage': 0.0,
            'avg_current': 0.0,
            'peak_current': 0.0,
            'min_current': 0.0,
            'max_current': 0.0,
            'latest_soc': None,
            'max_cell_delta': 0.0,
            'monitoring_gaps': 0,
            'min_cell_voltage': None,
            'max_cell_voltage': None,
            'min_pack_voltage': None,
            'max_pack_voltage': None
        }
        
        # Auto-start specific tracking
        self.charge_discharge_events: List[Dict[str, Any]] = []
        
        # User interaction
        self.notes: str = ""
        self.created_by: str = "GUI"  # 'GUI' or 'CLI'
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert session to dictionary for JSON storage"""
        return {
            'session_id': self.session_id,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'duration_seconds': self.duration_seconds,
            'status': self.status,
            'trigger_type': self.trigger_type,
            'auto_start_threshold': self.auto_start_threshold,
            'auto_end_enabled': self.auto_end_enabled,
            'recovery_phase': self.recovery_phase,
            'csv_files': self.csv_files,
            'connection_params': self.connection_params,
            'data_summary': self.data_summary,
            'charge_discharge_events': self.charge_discharge_events,
            'notes': self.notes,
            'created_by': self.created_by
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Session':
        """Create session from dictionary"""
        session = cls()
        session.session_id = data.get('session_id', str(uuid.uuid4()))
        
        start_time_str = data.get('start_time')
        if start_time_str:
            session.start_time = datetime.fromisoformat(start_time_str)
        
        end_time_str = data.get('end_time')
        if end_time_str:
            session.end_time = datetime.fromisoformat(end_time_str)
        
        session.duration_seconds = data.get('duration_seconds', 0)
        session.status = data.get('status', 'pending')
        session.trigger_type = data.get('trigger_type', 'manual')
        session.auto_start_threshold = data.get('auto_start_threshold')
        session.auto_end_enabled = data.get('auto_end_enabled', False)
        session.recovery_phase = data.get('recovery_phase', False)
        session.csv_files = data.get('csv_files', [])
        session.connection_params = data.get('connection_params', {})
        session.data_summary = data.get('data_summary', {})
        session.charge_discharge_events = data.get('charge_discharge_events', [])
        session.notes = data.get('notes', "")
        session.created_by = data.get('created_by', "GUI")
        
        return session


class AutoStartConfig:
    """Configuration for auto-start logging functionality"""
    
    def __init__(self):
        # Auto-start mode control
        self.auto_log_mode: str = 'OFF'  # 'OFF', 'ENABLE'
        
        # Threshold settings (in Amps)
        self.auto_log_charging_threshold: float = 0.5
        self.auto_log_discharging_threshold: float = -0.5
        
        # Feature enablement
        self.auto_log_charge_enable: bool = True
        self.auto_log_discharge_enable: bool = True
        
        # Auto-end timing (in minutes)
        self.auto_log_charge_end_time: int = 30
        self.auto_log_discharge_end_time: int = 30
        
        # Recovery logging
        self.pack_recovery_logging: bool = True
        self.recovery_duration_minutes: int = 15
        
        # Cooldown to prevent rapid re-triggering (in seconds)
        self.cooldown_seconds: int = 60
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary for storage"""
        return {
            'auto_log_mode': self.auto_log_mode,
            'auto_log_charging_threshold': self.auto_log_charging_threshold,
            'auto_log_discharging_threshold': self.auto_log_discharging_threshold,
            'auto_log_charge_enable': self.auto_log_charge_enable,
            'auto_log_discharge_enable': self.auto_log_discharge_enable,
            'auto_log_charge_end_time': self.auto_log_charge_end_time,
            'auto_log_discharge_end_time': self.auto_log_discharge_end_time,
            'pack_recovery_logging': self.pack_recovery_logging,
            'recovery_duration_minutes': self.recovery_duration_minutes,
            'cooldown_seconds': self.cooldown_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AutoStartConfig':
        """Create config from dictionary"""
        config = cls()
        config.auto_log_mode = data.get('auto_log_mode', 'OFF')
        config.auto_log_charging_threshold = data.get('auto_log_charging_threshold', 0.5)
        config.auto_log_discharging_threshold = data.get('auto_log_discharging_threshold', -0.5)
        config.auto_log_charge_enable = data.get('auto_log_charge_enable', True)
        config.auto_log_discharge_enable = data.get('auto_log_discharge_enable', True)
        config.auto_log_charge_end_time = data.get('auto_log_charge_end_time', 30)
        config.auto_log_discharge_end_time = data.get('auto_log_discharge_end_time', 30)
        config.pack_recovery_logging = data.get('pack_recovery_logging', True)
        config.recovery_duration_minutes = data.get('recovery_duration_minutes', 15)
        config.cooldown_seconds = data.get('cooldown_seconds', 60)
        return config


class SessionManager(QObject):
    """
    Manages monitoring session lifecycle and auto-start functionality.
    
    Handles both manual and automatic session creation, session data persistence,
    and integration with the existing application architecture.
    """
    
    # Signals
    session_started = pyqtSignal(str, str)  # session_id, trigger_type
    session_ended = pyqtSignal(str, dict)   # session_id, session_summary
    auto_start_triggered = pyqtSignal(str, float)  # trigger_type, current_value
    session_updated = pyqtSignal(str, dict)  # session_id, updated_data
    
    # QSettings keys
    SESSIONS_BASE_KEY = "sessions"
    CURRENT_SESSION_KEY = f"{SESSIONS_BASE_KEY}/current_session_id"
    SESSION_COUNT_KEY = f"{SESSIONS_BASE_KEY}/session_count"
    AUTO_CONFIG_KEY = "auto_start_config"
    
    def __init__(self, settings: QSettings):
        super().__init__()
        self.logger = get_logger(__name__ + '.SessionManager')
        self.logger.info("Initializing SessionManager")
        
        self.settings = settings
        self.current_session: Optional[Session] = None
        self.auto_config: AutoStartConfig = AutoStartConfig()
        self.last_auto_trigger_time: Optional[datetime] = None
        
        # Load configuration and restore active session
        try:
            self.load_auto_config()
            self.restore_active_session()
            self.logger.info("SessionManager initialization completed")
        except Exception as e:
            self.logger.error(f"SessionManager initialization failed: {e}")
            log_exception(self.logger, "SessionManager initialization error")
            raise
    
    def get_session_key(self, session_id: str, field: str) -> str:
        """Generate QSettings key for session field"""
        return f"{self.SESSIONS_BASE_KEY}/sessions/{session_id}/{field}"
    
    def load_auto_config(self):
        """Load auto-start configuration from QSettings"""
        config_json = self.settings.value(self.AUTO_CONFIG_KEY, "{}")
        if config_json and config_json != "{}":
            try:
                config_dict = json.loads(config_json)
                self.auto_config = AutoStartConfig.from_dict(config_dict)
            except (json.JSONDecodeError, Exception):
                # Use default config if loading fails
                self.auto_config = AutoStartConfig()
    
    def save_auto_config(self):
        """Save auto-start configuration to QSettings"""
        config_json = json.dumps(self.auto_config.to_dict())
        self.settings.setValue(self.AUTO_CONFIG_KEY, config_json)
        self.settings.sync()
    
    def restore_active_session(self):
        """Restore active session after application restart"""
        current_session_id = self.settings.value(self.CURRENT_SESSION_KEY)
        if current_session_id:
            session = self.load_session(current_session_id)
            if session and session.status == 'active':
                self.current_session = session
    
    def start_manual_session(self, connection_params: Dict[str, Any]) -> str:
        """Create and start a user-initiated monitoring session"""
        log_function_entry(self.logger, "start_manual_session", **connection_params)
        
        # End any existing session first
        if self.current_session:
            self.logger.info(f"Ending existing session: {self.current_session.session_id}")
            self.end_session()
        
        # Create new session
        session = Session()
        session.start_time = datetime.now()
        session.status = 'active'
        session.trigger_type = 'manual'
        session.connection_params = connection_params.copy()
        session.created_by = 'GUI'
        
        self.current_session = session
        self.save_session(session)
        self.settings.setValue(self.CURRENT_SESSION_KEY, session.session_id)
        self.settings.sync()
        
        self.logger.info(f"Started manual session: {session.session_id}")
        self.session_started.emit(session.session_id, session.trigger_type)
        
        log_function_exit(self.logger, "start_manual_session", session.session_id)
        return session.session_id
    
    def start_auto_session(self, trigger_type: str, current_value: float, 
                          connection_params: Dict[str, Any]) -> str:
        """Create and start an automatically triggered monitoring session"""
        # Check cooldown period
        if self.last_auto_trigger_time:
            time_since_last = datetime.now() - self.last_auto_trigger_time
            if time_since_last.total_seconds() < self.auto_config.cooldown_seconds:
                return None
        
        # End any existing session first
        if self.current_session:
            self.end_session()
        
        # Create new auto session
        session = Session()
        session.start_time = datetime.now()
        session.status = 'active'
        session.trigger_type = trigger_type
        session.auto_start_threshold = current_value
        session.connection_params = connection_params.copy()
        session.created_by = 'GUI'
        
        # Determine auto-end settings
        if trigger_type == 'auto_charge':
            session.auto_end_enabled = True
        elif trigger_type == 'auto_discharge':
            session.auto_end_enabled = True
        
        # Record trigger event
        session.charge_discharge_events.append({
            'event_type': f'{trigger_type.split("_")[1]}_start',
            'timestamp': datetime.now().isoformat(),
            'current_value': current_value,
            'trigger_threshold': (self.auto_config.auto_log_charging_threshold 
                                if 'charge' in trigger_type 
                                else self.auto_config.auto_log_discharging_threshold)
        })
        
        self.current_session = session
        self.save_session(session)
        self.settings.setValue(self.CURRENT_SESSION_KEY, session.session_id)
        self.settings.sync()
        
        self.last_auto_trigger_time = datetime.now()
        self.auto_start_triggered.emit(trigger_type, current_value)
        self.session_started.emit(session.session_id, session.trigger_type)
        return session.session_id
    
    def end_session(self, session_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """End current or specified session"""
        session = self.current_session if session_id is None else self.load_session(session_id)
        
        if not session:
            return None
        
        # Update session end data
        session.end_time = datetime.now()
        if session.start_time:
            session.duration_seconds = int((session.end_time - session.start_time).total_seconds())
        session.status = 'completed'
        
        # Save final session state
        self.save_session(session)
        
        # Clear current session if it's the one being ended
        if self.current_session and self.current_session.session_id == session.session_id:
            self.current_session = None
            self.settings.remove(self.CURRENT_SESSION_KEY)
            self.settings.sync()
        
        session_summary = session.to_dict()
        self.session_ended.emit(session.session_id, session_summary)
        return session_summary
    
    def check_auto_start_conditions(self, current_value: float, 
                                  connection_params: Dict[str, Any]) -> Optional[str]:
        """
        Check if auto-start conditions are met and trigger session if needed.
        
        Returns:
            str: Session ID if auto-start triggered, None otherwise
        """
        if self.auto_config.auto_log_mode != 'ENABLE':
            return None
        
        if self.current_session is not None:
            return None  # Session already active
        
        # Check cooldown period
        if self.last_auto_trigger_time:
            time_since_last = datetime.now() - self.last_auto_trigger_time
            if time_since_last.total_seconds() < self.auto_config.cooldown_seconds:
                return None
        
        # Check charging threshold
        if (self.auto_config.auto_log_charge_enable and 
            current_value >= self.auto_config.auto_log_charging_threshold):
            return self.start_auto_session('auto_charge', current_value, connection_params)
        
        # Check discharging threshold  
        if (self.auto_config.auto_log_discharge_enable and
            current_value <= self.auto_config.auto_log_discharging_threshold):
            return self.start_auto_session('auto_discharge', current_value, connection_params)
        
        return None
    
    def update_session_data(self, data: Dict[str, Any]):
        """Update current session with new data point"""
        if not self.current_session:
            return
        
        session = self.current_session
        session.data_summary['total_records'] += 1
        
        # Update timing
        current_time = datetime.now()
        if session.data_summary['data_start_time'] is None:
            session.data_summary['data_start_time'] = current_time.isoformat()
        session.data_summary['data_end_time'] = current_time.isoformat()
        
        # Update averages and extremes
        if 'cell_voltages' in data:
            avg_cell = sum(data['cell_voltages']) / len(data['cell_voltages'])
            self._update_running_average('avg_cell_voltage', avg_cell)
            
            min_cell = min(data['cell_voltages'])
            max_cell = max(data['cell_voltages'])
            self._update_min_max('min_cell_voltage', 'max_cell_voltage', min_cell, max_cell)
        
        if 'pack_voltage' in data:
            self._update_running_average('avg_pack_voltage', data['pack_voltage'])
            self._update_min_max('min_pack_voltage', 'max_pack_voltage', 
                               data['pack_voltage'], data['pack_voltage'])
        
        if 'current' in data:
            current = data['current']
            self._update_running_average('avg_current', current)
            
            # Track peak current (absolute maximum)
            if abs(current) > abs(session.data_summary.get('peak_current', 0)):
                session.data_summary['peak_current'] = current
            
            # Track min/max current
            if session.data_summary['min_current'] is None or current < session.data_summary['min_current']:
                session.data_summary['min_current'] = current
            if session.data_summary['max_current'] is None or current > session.data_summary['max_current']:
                session.data_summary['max_current'] = current
        
        # Track SOC if available
        if 'soc' in data:
            session.data_summary['latest_soc'] = data['soc']
        
        # Track cell delta if available
        if 'cell_delta' in data:
            if data['cell_delta'] > session.data_summary.get('max_cell_delta', 0):
                session.data_summary['max_cell_delta'] = data['cell_delta']
        
        # Save updated session periodically (every 10 records to avoid excessive I/O)
        if session.data_summary['total_records'] % 10 == 0:
            self.save_session(session)
        
        self.session_updated.emit(session.session_id, data)
    
    def _update_running_average(self, field: str, new_value: float):
        """Update running average for a field"""
        records = self.current_session.data_summary['total_records']
        current_avg = self.current_session.data_summary[field]
        
        if records == 1:
            self.current_session.data_summary[field] = new_value
        else:
            # Running average: new_avg = (old_avg * (n-1) + new_value) / n
            self.current_session.data_summary[field] = (
                (current_avg * (records - 1) + new_value) / records
            )
    
    def _update_min_max(self, min_field: str, max_field: str, new_value: float, max_value: float):
        """Update min/max values for a field"""
        current_min = self.current_session.data_summary[min_field]
        current_max = self.current_session.data_summary[max_field]
        
        if current_min is None or new_value < current_min:
            self.current_session.data_summary[min_field] = new_value
        
        if current_max is None or max_value > current_max:
            self.current_session.data_summary[max_field] = max_value
    
    def associate_csv_file(self, csv_filename: str):
        """Associate CSV file with current session"""
        if self.current_session and csv_filename not in self.current_session.csv_files:
            self.current_session.csv_files.append(csv_filename)
            self.save_session(self.current_session)
            
            # Add session metadata to CSV file
            try:
                from ..ga_modbus_csv_writer import add_session_metadata_to_csv
                csv_path = f"log/{csv_filename}" if not csv_filename.startswith("log/") else csv_filename
                add_session_metadata_to_csv(csv_path, self.current_session.to_dict())
            except (ImportError, Exception) as e:
                # Fail silently if CSV metadata addition fails
                pass
    
    def save_session(self, session: Session):
        """Save session data to QSettings"""
        session_json = json.dumps(session.to_dict())
        key = self.get_session_key(session.session_id, 'data')
        self.settings.setValue(key, session_json)
        
        # Update session count
        session_count = self.settings.value(self.SESSION_COUNT_KEY, 0, type=int)
        self.settings.setValue(self.SESSION_COUNT_KEY, session_count + 1)
        self.settings.sync()
    
    def load_session(self, session_id: str) -> Optional[Session]:
        """Load session data from QSettings"""
        key = self.get_session_key(session_id, 'data')
        session_json = self.settings.value(key)
        
        if session_json:
            try:
                session_dict = json.loads(session_json)
                return Session.from_dict(session_dict)
            except (json.JSONDecodeError, Exception):
                return None
        return None
    
    def get_session_history(self) -> List[Session]:
        """Retrieve all stored sessions"""
        log_function_entry(self.logger, "get_session_history")
        sessions = []
        
        try:
            # Get all session keys
            self.settings.beginGroup(f"{self.SESSIONS_BASE_KEY}/sessions")
            session_ids = self.settings.childGroups()
            self.settings.endGroup()
            
            self.logger.info(f"Found {len(session_ids)} stored sessions")
            
            # Load each session
            for session_id in session_ids:
                session = self.load_session(session_id)
                if session:
                    sessions.append(session)
                else:
                    self.logger.warning(f"Failed to load session: {session_id}")
            
            # Sort by start time (newest first)
            sessions.sort(key=lambda s: s.start_time or datetime.min, reverse=True)
            self.logger.info(f"Successfully loaded {len(sessions)} sessions")
            
        except Exception as e:
            self.logger.error(f"Error retrieving session history: {e}")
            log_exception(self.logger, "Session history retrieval error")
        
        log_function_exit(self.logger, "get_session_history", len(sessions))
        return sessions
    
    def delete_session(self, session_id: str) -> bool:
        """Delete a session from storage"""
        try:
            # Remove session data
            key = self.get_session_key(session_id, 'data')
            self.settings.remove(key)
            
            # Remove session group
            self.settings.beginGroup(f"{self.SESSIONS_BASE_KEY}/sessions")
            self.settings.remove(session_id)
            self.settings.endGroup()
            
            self.settings.sync()
            return True
        except Exception:
            return False
    
    def add_session_note(self, session_id: str, note: str) -> bool:
        """Add or update note for a session"""
        session = self.load_session(session_id)
        if session:
            session.notes = note
            self.save_session(session)
            return True
        return False
    
    def get_current_session_status(self) -> Dict[str, Any]:
        """Get current session status for UI display"""
        if not self.current_session:
            return {
                'active': False,
                'session_id': None,
                'trigger_type': None,
                'duration': 0,
                'record_count': 0
            }
        
        duration = 0
        if self.current_session.start_time:
            duration = int((datetime.now() - self.current_session.start_time).total_seconds())
        
        return {
            'active': True,
            'session_id': self.current_session.session_id,
            'trigger_type': self.current_session.trigger_type,
            'duration': duration,
            'record_count': self.current_session.data_summary['total_records'],
            'csv_files': len(self.current_session.csv_files)
        }