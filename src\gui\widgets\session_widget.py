#!/usr/bin/env python3
"""
Session Management Widget
Handles session history, auto-start configuration, and session statistics
"""

import os
import csv
from datetime import datetime
from typing import Dict, List, Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QH<PERSON><PERSON>Layout, QTableWidget, QTableWidgetItem,
    QPushButton, QGroupBox, QFormLayout, QComboBox, QDoubleSpinBox,
    QCheckBox, QLabel, QHeaderView, QMessageBox, QProgressBar
)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QColor

from ...utils.constants import DEFAULT_LOG_DIR
from ...utils.logger import get_logger, log_exception, log_function_entry, log_function_exit


class SessionAnalyzerWorker(QThread):
    """Worker thread for analyzing session CSV files"""
    
    progressChanged = pyqtSignal(int)  # Progress percentage
    sessionAnalyzed = pyqtSignal(str, dict)  # Session ID, analysis results
    analysisComplete = pyqtSignal()
    
    def __init__(self, session_files: List[str]):
        super().__init__()
        self.session_files = session_files
        self.logger = get_logger(__name__ + '.SessionAnalyzerWorker')
        
    def run(self):
        """Analyze all session files"""
        total_files = len(self.session_files)
        self.logger.info(f"Starting analysis of {total_files} session files")
        
        for i, filepath in enumerate(self.session_files):
            try:
                self.logger.debug(f"Analyzing file: {filepath}")
                analysis = self.analyze_session_file(filepath)
                session_id = os.path.basename(filepath).replace('.csv', '')
                self.sessionAnalyzed.emit(session_id, analysis)
            except Exception as e:
                self.logger.error(f"Error analyzing {filepath}: {e}")
                log_exception(self.logger, f"Session analysis error for {filepath}")
                
            # Update progress
            progress = int((i + 1) / total_files * 100)
            self.progressChanged.emit(progress)
            
        self.logger.info("Session analysis completed")
        self.analysisComplete.emit()
        
    def analyze_session_file(self, filepath: str) -> Dict:
        """Analyze a single session CSV file"""
        analysis = {
            'event_type': 'Unknown',
            'voltage_range': 'N/A',
            'peak_current': 'N/A',
            'cell_delta_max': 'N/A',
            'soc': 'N/A',
            'duration': 'N/A',
            'records': 0
        }
        
        if not os.path.exists(filepath):
            return analysis
            
        try:
            with open(filepath, 'r') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
                
            if not rows:
                self.logger.warning(f"No data rows found in {filepath}")
                return analysis
                
            # Debug: log available columns
            if rows:
                available_columns = list(rows[0].keys())
                self.logger.debug(f"CSV columns in {filepath}: {available_columns}")
                
            analysis['records'] = len(rows)
            
            # Extract data columns
            currents = []
            pack_voltages = []
            cell_deltas = []
            soc_values = []
            cell_voltages_by_row = []  # Store cell voltages for each row to find high delta cell group
            
            for row in rows:
                # Extract cell voltages for this row
                cell_voltages = []
                for i in range(1, 9):  # Cell1_V through Cell8_V
                    cell_col = f'Cell{i}_V'
                    if cell_col in row and row[cell_col]:
                        try:
                            voltage = float(row[cell_col])
                            # Scale from mV to V if needed
                            if voltage > 1000:
                                voltage = voltage / 1000.0
                            cell_voltages.append(voltage)
                        except ValueError:
                            pass
                
                if len(cell_voltages) >= 2:  # Need at least 2 cells to calculate delta
                    cell_voltages_by_row.append(cell_voltages)
                
                # Current analysis - check multiple possible column names
                current_columns = ['Current', 'Afe Current', 'current', 'Current_A', 'Pack_Current']
                for col in current_columns:
                    if col in row and row[col]:
                        try:
                            current = float(row[col])
                            currents.append(current)
                            break  # Found current, stop checking other columns
                        except ValueError:
                            continue
                        
                # Pack voltage analysis - check multiple possible column names
                voltage_columns = ['Pack_V', 'Pack Volt', 'Pack_Voltage', 'Afe Pack Volt', 'Pack_V_V']
                for col in voltage_columns:
                    if col in row and row[col]:
                        try:
                            voltage = float(row[col])
                            # Scale from mV to V for CSV data
                            if voltage > 1000:  # Assume values > 1000 are in mV
                                voltage = voltage / 1000.0
                            pack_voltages.append(voltage)
                            break  # Found voltage, stop checking other columns
                        except ValueError:
                            continue
                        
                # Cell delta analysis - check multiple possible column names
                delta_columns = ['Cell_Delta_V', 'Afe Cell Delta', 'Cell Delta', 'Cell_Delta', 'Delta_V']
                for col in delta_columns:
                    if col in row and row[col]:
                        try:
                            delta = float(row[col])
                            # Scale from mV to V for CSV data
                            if delta > 1:  # Assume values > 1 are in mV (since cell deltas are typically < 1V)
                                delta = delta / 1000.0
                            cell_deltas.append(delta)
                            break  # Found delta, stop checking other columns
                        except ValueError:
                            continue
                        
                # SOC analysis - check multiple possible column names
                soc_columns = ['SOC', 'Fg State Of Charge', 'State_Of_Charge', 'SOC_%', 'Fuel_Gauge_SOC']
                for col in soc_columns:
                    if col in row and row[col]:
                        try:
                            soc = float(row[col])
                            soc_values.append(soc)
                            break  # Found SOC, stop checking other columns
                        except ValueError:
                            continue
            
            # Determine event type and peak current
            if currents:
                max_current = max(currents)
                min_current = min(currents)
                avg_current = sum(currents) / len(currents)
                
                # Debug logging
                self.logger.debug(f"Current analysis for {filepath}: avg={avg_current:.3f}, min={min_current:.3f}, max={max_current:.3f}, count={len(currents)}")
                
                if avg_current > 0.1:  # Charging threshold
                    analysis['event_type'] = 'Charge'
                    analysis['peak_current'] = f"{max_current:.3f}A"
                elif avg_current < -0.1:  # Discharging threshold
                    analysis['event_type'] = 'Discharge'
                    analysis['peak_current'] = f"{min_current:.3f}A"
                else:
                    analysis['event_type'] = 'Idle'
                    analysis['peak_current'] = f"{avg_current:.3f}A"
            else:
                self.logger.warning(f"No current data found in {filepath}")
                # Try to debug what columns are available
                if rows:
                    available_columns = list(rows[0].keys())
                    self.logger.debug(f"Available columns: {available_columns}")
            
            # Voltage range
            if pack_voltages and len(pack_voltages) > 1:
                start_voltage = pack_voltages[0]
                end_voltage = pack_voltages[-1]
                analysis['voltage_range'] = f"{start_voltage:.2f}V -> {end_voltage:.2f}V"
            elif pack_voltages:
                analysis['voltage_range'] = f"{pack_voltages[0]:.2f}V"
                
            # Cell delta range (min -> max) - display in mV
            if cell_deltas and len(cell_deltas) > 1:
                min_delta = min(cell_deltas)
                max_delta = max(cell_deltas)
                # Convert to mV for display
                min_delta_mv = min_delta * 1000
                max_delta_mv = max_delta * 1000
                analysis['cell_delta_max'] = f"{min_delta_mv:.0f}mV -> {max_delta_mv:.0f}mV"
                self.logger.debug(f"Cell delta analysis for {filepath}: {min_delta:.3f}V -> {max_delta:.3f}V ({min_delta_mv:.0f}mV -> {max_delta_mv:.0f}mV), count={len(cell_deltas)}")
            elif cell_deltas:
                # Single cell delta value
                delta = cell_deltas[0]
                delta_mv = delta * 1000
                analysis['cell_delta_max'] = f"{delta_mv:.0f}mV"
                self.logger.debug(f"Cell delta analysis for {filepath}: {delta:.3f}V ({delta_mv:.0f}mV) (single value)")
            else:
                self.logger.warning(f"No cell delta data found in {filepath}")
                
            # Find cell group with highest delta
            cell_group_high_delta = 'N/A'
            if cell_voltages_by_row:
                max_delta_overall = 0
                max_delta_cell_group = None
                max_delta_row = None
                
                for row_idx, cell_voltages in enumerate(cell_voltages_by_row):
                    if len(cell_voltages) >= 2:
                        min_voltage = min(cell_voltages)
                        max_voltage = max(cell_voltages)
                        row_delta = max_voltage - min_voltage
                        
                        if row_delta > max_delta_overall:
                            max_delta_overall = row_delta
                            max_delta_row = row_idx
                            # Find which cell has the max voltage in this row
                            max_voltage_index = cell_voltages.index(max_voltage)
                            max_delta_cell_group = max_voltage_index + 1  # Cell numbers are 1-based
                
                if max_delta_cell_group is not None:
                    cell_group_high_delta = f"Cell {max_delta_cell_group}"
                    self.logger.debug(f"Cell group analysis for {filepath}: Cell {max_delta_cell_group} had highest delta ({max_delta_overall*1000:.0f}mV) in row {max_delta_row}")
                    
            analysis['cell_group_high_delta'] = cell_group_high_delta
                
            # SOC range (start -> end)
            if soc_values and len(soc_values) > 1:
                start_soc = soc_values[0]
                end_soc = soc_values[-1]
                analysis['soc'] = f"{start_soc:.1f}% -> {end_soc:.1f}%"
                self.logger.debug(f"SOC analysis for {filepath}: {start_soc:.1f}% -> {end_soc:.1f}%, count={len(soc_values)}")
            elif soc_values:
                # Single SOC value
                soc = soc_values[0]
                analysis['soc'] = f"{soc:.1f}%"
                self.logger.debug(f"SOC analysis for {filepath}: {soc:.1f}% (single value)")
            else:
                self.logger.warning(f"No SOC data found in {filepath}")
                
            # Calculate duration from timestamps
            if len(rows) > 1:
                try:
                    start_time = datetime.fromisoformat(rows[0]['Timestamp'])
                    end_time = datetime.fromisoformat(rows[-1]['Timestamp'])
                    duration = end_time - start_time
                    analysis['duration'] = str(duration).split('.')[0]  # Remove microseconds
                except (KeyError, ValueError):
                    pass
                    
        except Exception as e:
            print(f"Error processing {filepath}: {e}")
            
        return analysis


class SessionWidget(QWidget):
    """Session management widget with enhanced analysis features"""
    
    def __init__(self, session_manager=None):
        super().__init__()
        self.logger = get_logger(__name__ + '.SessionWidget')
        self.logger.info("Initializing SessionWidget")
        
        self.session_manager = session_manager
        self.analyzer_worker = None
        self.session_analysis = {}  # Store analysis results
        
        self.setup_ui()
        self.setup_connections()
        
        # Auto-load session history when widget is created
        QTimer.singleShot(100, self.refresh_session_history)
        
        # Setup timer for periodic session table updates (to show real-time duration)
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_active_session_display)
        self.update_timer.start(10000)  # Update every 10 seconds
        
        self.logger.info("SessionWidget initialization completed")
        
    def setup_ui(self):
        """Setup the session widget UI"""
        layout = QVBoxLayout(self)
        
        # Current session info
        current_session_group = QGroupBox("Current Session")
        current_layout = QFormLayout(current_session_group)
        
        self.current_session_label = QLabel("Status: No active session")
        current_layout.addRow("", self.current_session_label)
        
        layout.addWidget(current_session_group)
        
        # Auto-start configuration
        autostart_group = QGroupBox("Auto-Start Configuration")
        autostart_layout = QFormLayout(autostart_group)
        
        self.auto_start_mode_combo = QComboBox()
        self.auto_start_mode_combo.addItems(["OFF", "Charging", "Discharging", "Both"])
        autostart_layout.addRow("Auto-Start Mode:", self.auto_start_mode_combo)
        
        self.charging_threshold_spin = QDoubleSpinBox()
        self.charging_threshold_spin.setRange(0.01, 10.0)
        self.charging_threshold_spin.setValue(0.50)
        self.charging_threshold_spin.setSuffix(" A")
        autostart_layout.addRow("Charging Threshold (A):", self.charging_threshold_spin)
        
        self.discharging_threshold_spin = QDoubleSpinBox()
        self.discharging_threshold_spin.setRange(-10.0, -0.01)
        self.discharging_threshold_spin.setValue(-0.50)
        self.discharging_threshold_spin.setSuffix(" A")
        autostart_layout.addRow("Discharging Threshold (A):", self.discharging_threshold_spin)
        
        self.enable_auto_start_charging = QCheckBox("Enable Auto-Start on Charging")
        self.enable_auto_start_discharging = QCheckBox("Enable Auto-Start on Discharging")
        autostart_layout.addRow(self.enable_auto_start_charging)
        autostart_layout.addRow(self.enable_auto_start_discharging)
        
        layout.addWidget(autostart_group)
        
        # Session history controls
        history_controls_layout = QHBoxLayout()
        
        self.refresh_history_btn = QPushButton("Refresh History")
        history_controls_layout.addWidget(self.refresh_history_btn)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        history_controls_layout.addWidget(self.progress_bar)
        
        history_controls_layout.addStretch()
        layout.addLayout(history_controls_layout)
        
        # Session history table
        self.session_table = QTableWidget()
        self.setup_session_table()
        layout.addWidget(self.session_table)
        
    def setup_session_table(self):
        """Setup the session history table with enhanced columns"""
        headers = [
            "Session ID", "Start Time", "Duration", "Event", 
            "Voltage Range", "Current", "SOC", "Cell Delta Max", 
            "Cell Group High Delta", "Records", "Files"
        ]
        
        self.session_table.setColumnCount(len(headers))
        self.session_table.setHorizontalHeaderLabels(headers)
        
        # Set column widths
        header = self.session_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # Session ID
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # Start Time
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Duration
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Event
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)           # Voltage Range
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # Current
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # SOC
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)  # Cell Delta Max
        header.setSectionResizeMode(8, QHeaderView.ResizeMode.ResizeToContents)  # Cell Group High Delta
        header.setSectionResizeMode(9, QHeaderView.ResizeMode.ResizeToContents)  # Records
        header.setSectionResizeMode(10, QHeaderView.ResizeMode.ResizeToContents) # Files
        
        # Set alternating row colors
        self.session_table.setAlternatingRowColors(True)
        
    def setup_connections(self):
        """Setup signal connections"""
        self.refresh_history_btn.clicked.connect(self.refresh_session_history)
        
    def refresh_session_history(self):
        """Refresh session history with enhanced analysis"""
        log_function_entry(self.logger, "refresh_session_history")
        
        # Get all session files
        log_dir = DEFAULT_LOG_DIR
        self.logger.info(f"Scanning log directory: {log_dir}")
        
        if not os.path.exists(log_dir):
            self.logger.warning(f"Log directory does not exist: {log_dir}")
            self.session_table.setRowCount(0)
            return
            
        csv_files = []
        # Get CSV files from main log directory
        for filename in os.listdir(log_dir):
            if filename.endswith('.csv'):
                filepath = os.path.join(log_dir, filename)
                csv_files.append(filepath)
        
        # Also check subdirectories
        for item in os.listdir(log_dir):
            item_path = os.path.join(log_dir, item)
            if os.path.isdir(item_path):
                try:
                    for filename in os.listdir(item_path):
                        if filename.endswith('.csv'):
                            filepath = os.path.join(item_path, filename)
                            csv_files.append(filepath)
                except (PermissionError, OSError):
                    continue
                
        if not csv_files:
            self.logger.info("No CSV files found in log directory")
            self.session_table.setRowCount(0)
            return
            
        self.logger.info(f"Found {len(csv_files)} CSV files to analyze")
        
        # Start analysis in background thread
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.refresh_history_btn.setEnabled(False)
        
        self.analyzer_worker = SessionAnalyzerWorker(csv_files)
        self.analyzer_worker.progressChanged.connect(self.progress_bar.setValue)
        self.analyzer_worker.sessionAnalyzed.connect(self.update_session_analysis)
        self.analyzer_worker.analysisComplete.connect(self.analysis_complete)
        self.analyzer_worker.start()
        
        log_function_exit(self.logger, "refresh_session_history")
        
    def update_session_analysis(self, csv_session_id: str, analysis: Dict):
        """Update analysis results for a session"""
        # The csv_session_id is from CSV filename, need to map to actual session ID
        self.session_analysis[csv_session_id] = analysis
        
        # Try to find matching session by CSV filename if session manager is available
        if self.session_manager:
            session_objects = self.session_manager.get_session_history()
            for session_obj in session_objects:
                session_dict = session_obj.to_dict()
                csv_files = session_dict.get('csv_files', [])
                
                # Check if this CSV file is associated with this session
                csv_filename = f"{csv_session_id}.csv"
                for csv_file in csv_files:
                    if csv_filename in csv_file or csv_session_id in csv_file:
                        # Found a match - store analysis under the real session ID too
                        real_session_id = session_dict['session_id']
                        self.session_analysis[real_session_id] = analysis
                        self.logger.debug(f"Mapped CSV {csv_session_id} to session {real_session_id}")
                        break
        
    def analysis_complete(self):
        """Handle completion of session analysis"""
        self.progress_bar.setVisible(False)
        self.refresh_history_btn.setEnabled(True)
        
        # Update table with analysis results
        self.populate_session_table()
        
    def populate_session_table(self):
        """Populate the session table with enhanced data"""
        sessions = []
        
        # Get session data from manager if available
        if self.session_manager:
            session_objects = self.session_manager.get_session_history()
            sessions = [session.to_dict() for session in session_objects]
        else:
            # Fallback: create sessions from analysis results
            for session_id, analysis in self.session_analysis.items():
                sessions.append({
                    'session_id': session_id,
                    'start_time': session_id[:8] if len(session_id) >= 8 else 'Unknown',
                    'trigger': 'manual',
                    'records': analysis.get('records', 0),
                    'files': 1
                })
        
        self.session_table.setRowCount(len(sessions))
        
        for row, session in enumerate(sessions):
            session_id = session.get('session_id', '')
            analysis = self.session_analysis.get(session_id, {})
            
            # Debug: log what analysis data we have for this session
            if analysis:
                self.logger.debug(f"Session {session_id[:8]}... has analysis: {analysis}")
            else:
                self.logger.debug(f"Session {session_id[:8]}... has no analysis data")
            
            # Session ID
            self.session_table.setItem(row, 0, QTableWidgetItem(session_id))
            
            # Start Time
            start_time = session.get('start_time', 'Unknown')
            if len(start_time) == 8 and start_time.isdigit():
                # Format YYYYMMDD to YYYY-MM-DD
                formatted_time = f"{start_time[:4]}-{start_time[4:6]}-{start_time[6:8]}"
                self.session_table.setItem(row, 1, QTableWidgetItem(formatted_time))
            else:
                self.session_table.setItem(row, 1, QTableWidgetItem(start_time))
            
            # Duration - prioritize session manager data over analysis
            duration = 'N/A'
            
            # If this is an active session, calculate real-time duration
            if (self.session_manager and 
                self.session_manager.current_session and 
                self.session_manager.current_session.session_id == session_id):
                
                if self.session_manager.current_session.start_time:
                    elapsed = datetime.now() - self.session_manager.current_session.start_time
                    duration = str(elapsed).split('.')[0]  # Remove microseconds
            elif 'duration_seconds' in session and session['duration_seconds'] > 0:
                # For completed sessions, use saved duration
                seconds = session['duration_seconds']
                hours, remainder = divmod(seconds, 3600)
                minutes, seconds = divmod(remainder, 60)
                if hours > 0:
                    duration = f"{hours}:{minutes:02d}:{seconds:02d}"
                else:
                    duration = f"{minutes}:{seconds:02d}"
            else:
                # Fallback to analysis duration
                duration = analysis.get('duration', 'N/A')
                    
            self.session_table.setItem(row, 2, QTableWidgetItem(duration))
            
            # Event Type - prioritize analysis over session trigger type
            event_type = 'Unknown'
            analysis_event = analysis.get('event_type', 'Unknown')
            session_trigger = session.get('trigger_type', 'unknown')
            
            self.logger.debug(f"Event type decision for {session_id[:8]}...: analysis='{analysis_event}', trigger='{session_trigger}'")
            
            # First try to get from analysis (CSV data analysis)
            if analysis_event and analysis_event != 'Unknown':
                event_type = analysis_event
                self.logger.debug(f"Using analysis event type: {event_type}")
            elif self.session_manager and session.get('trigger_type'):
                # Map session trigger types to display names
                trigger_type = session['trigger_type']
                if trigger_type == 'auto_charge':
                    event_type = 'Auto Charge'
                elif trigger_type == 'auto_discharge':
                    event_type = 'Auto Discharge'
                elif trigger_type == 'manual':
                    event_type = 'Manual'
                else:
                    event_type = trigger_type.title()
                self.logger.debug(f"Using session trigger type: {event_type}")
            else:
                # Final fallback to analysis or 'Unknown'
                event_type = analysis.get('event_type', 'Unknown')
                self.logger.debug(f"Using fallback event type: {event_type}")
            
            item = QTableWidgetItem(event_type)
            if 'Charge' in event_type:
                item.setBackground(QColor(144, 238, 144))  # lightGreen
            elif 'Discharge' in event_type:
                item.setBackground(QColor(255, 255, 224))  # lightYellow
            elif event_type == 'Idle':
                item.setBackground(QColor(211, 211, 211))  # lightGray
            elif event_type == 'Manual':
                item.setBackground(QColor(173, 216, 230))  # lightBlue
            self.session_table.setItem(row, 3, item)
            
            # Voltage Range - prefer session manager data for active sessions
            if (self.session_manager and 
                self.session_manager.current_session and 
                self.session_manager.current_session.session_id == session_id):
                # For active session, get data from session manager
                data_summary = self.session_manager.current_session.data_summary
                min_pack = data_summary.get('min_pack_voltage')
                max_pack = data_summary.get('max_pack_voltage')
                if min_pack is not None and max_pack is not None:
                    voltage_range = f"{min_pack:.2f}V -> {max_pack:.2f}V"
                else:
                    voltage_range = analysis.get('voltage_range', 'N/A')
            else:
                # For completed sessions, try session data first, then analysis
                data_summary = session.get('data_summary', {})
                min_pack = data_summary.get('min_pack_voltage')
                max_pack = data_summary.get('max_pack_voltage')
                if min_pack is not None and max_pack is not None:
                    voltage_range = f"{min_pack:.2f}V -> {max_pack:.2f}V"
                else:
                    voltage_range = analysis.get('voltage_range', 'N/A')
            self.session_table.setItem(row, 4, QTableWidgetItem(voltage_range))
            
            # Peak Current - show actual peak, not average
            if (self.session_manager and 
                self.session_manager.current_session and 
                self.session_manager.current_session.session_id == session_id):
                # For active session, show peak current from session manager
                peak_current_val = self.session_manager.current_session.data_summary.get('peak_current', 0.0)
                if peak_current_val != 0.0:
                    peak_current = f"{peak_current_val:.3f}A"
                else:
                    peak_current = "0.000A"
            else:
                # For completed sessions, try session data first, then analysis
                data_summary = session.get('data_summary', {})
                peak_current_val = data_summary.get('peak_current')
                if peak_current_val is not None:
                    peak_current = f"{peak_current_val:.3f}A"
                else:
                    peak_current = analysis.get('peak_current', 'N/A')
            self.session_table.setItem(row, 5, QTableWidgetItem(peak_current))
            
            # SOC - prefer session manager data for active sessions
            if (self.session_manager and 
                self.session_manager.current_session and 
                self.session_manager.current_session.session_id == session_id):
                # For active session, show latest SOC from session manager
                latest_soc = self.session_manager.current_session.data_summary.get('latest_soc')
                if latest_soc is not None:
                    soc = f"{latest_soc:.1f}%"
                else:
                    soc = "N/A"
            else:
                # For completed sessions, try session data first, then analysis
                data_summary = session.get('data_summary', {})
                latest_soc = data_summary.get('latest_soc')
                if latest_soc is not None:
                    soc = f"{latest_soc:.1f}%"
                else:
                    soc = analysis.get('soc', 'N/A')
            self.session_table.setItem(row, 6, QTableWidgetItem(soc))
            
            # Cell Delta Max - prefer session manager data for active sessions
            if (self.session_manager and 
                self.session_manager.current_session and 
                self.session_manager.current_session.session_id == session_id):
                # For active session, show max cell delta from session manager
                max_delta = self.session_manager.current_session.data_summary.get('max_cell_delta', 0.0)
                if max_delta > 0.0:
                    cell_delta_max = f"{max_delta:.3f}V"
                else:
                    cell_delta_max = "N/A"
            else:
                # For completed sessions, try session data first, then analysis
                data_summary = session.get('data_summary', {})
                max_delta = data_summary.get('max_cell_delta')
                if max_delta is not None and max_delta > 0.0:
                    cell_delta_max = f"{max_delta:.3f}V"
                else:
                    cell_delta_max = analysis.get('cell_delta_max', 'N/A')
            self.session_table.setItem(row, 7, QTableWidgetItem(cell_delta_max))
            
            # Cell Group High Delta - get from analysis
            cell_group_high_delta = analysis.get('cell_group_high_delta', 'N/A')
            self.session_table.setItem(row, 8, QTableWidgetItem(cell_group_high_delta))
            
            # Records - get from session data summary or analysis
            if (self.session_manager and 
                self.session_manager.current_session and 
                self.session_manager.current_session.session_id == session_id):
                # For active session, get real-time record count
                records = str(self.session_manager.current_session.data_summary['total_records'])
            else:
                # For completed sessions, use stored data
                data_summary = session.get('data_summary', {})
                records = str(data_summary.get('total_records', analysis.get('records', 0)))
            self.session_table.setItem(row, 9, QTableWidgetItem(records))
            
            # Files - show actual CSV file count for the session
            if self.session_manager and self.session_manager.current_session and self.session_manager.current_session.session_id == session_id:
                # For active session, get real file count
                files = str(len(self.session_manager.current_session.csv_files))
            else:
                # For completed sessions, try to get file count from session data
                files = str(session.get('files', len(session.get('csv_files', [])) if 'csv_files' in session else 0))
            self.session_table.setItem(row, 10, QTableWidgetItem(files))
            
    def update_current_session(self, session_info: str):
        """Update current session display"""
        self.current_session_label.setText(session_info)
        
    def get_auto_start_config(self) -> Dict:
        """Get auto-start configuration"""
        return {
            'mode': self.auto_start_mode_combo.currentText(),
            'charging_threshold': self.charging_threshold_spin.value(),
            'discharging_threshold': self.discharging_threshold_spin.value(),
            'enable_charging': self.enable_auto_start_charging.isChecked(),
            'enable_discharging': self.enable_auto_start_discharging.isChecked()
        }
        
    def set_auto_start_config(self, config: Dict):
        """Set auto-start configuration"""
        self.auto_start_mode_combo.setCurrentText(config.get('mode', 'OFF'))
        self.charging_threshold_spin.setValue(config.get('charging_threshold', 0.50))
        self.discharging_threshold_spin.setValue(config.get('discharging_threshold', -0.50))
        self.enable_auto_start_charging.setChecked(config.get('enable_charging', False))
        self.enable_auto_start_discharging.setChecked(config.get('enable_discharging', False))
        
    def update_active_session_display(self):
        """Update the display for active sessions with real-time information"""
        if not self.session_manager or not self.session_manager.current_session:
            return
            
        # Find the active session row in the table and update its duration
        current_session_id = self.session_manager.current_session.session_id
        
        for row in range(self.session_table.rowCount()):
            session_id_item = self.session_table.item(row, 0)
            if session_id_item and session_id_item.text() == current_session_id:
                # Update duration for this row
                if self.session_manager.current_session.start_time:
                    elapsed = datetime.now() - self.session_manager.current_session.start_time
                    duration = str(elapsed).split('.')[0]  # Remove microseconds
                    self.session_table.setItem(row, 2, QTableWidgetItem(duration))
                
                # Update record count
                record_count = self.session_manager.current_session.data_summary['total_records']
                self.session_table.setItem(row, 9, QTableWidgetItem(str(record_count)))
                
                # Update file count
                file_count = len(self.session_manager.current_session.csv_files)
                self.session_table.setItem(row, 10, QTableWidgetItem(str(file_count)))
                
                # Update statistics columns with real-time data
                data_summary = self.session_manager.current_session.data_summary
                
                # Update voltage range
                min_pack = data_summary.get('min_pack_voltage')
                max_pack = data_summary.get('max_pack_voltage')
                if min_pack is not None and max_pack is not None:
                    voltage_range = f"{min_pack:.2f}V -> {max_pack:.2f}V"
                    self.session_table.setItem(row, 4, QTableWidgetItem(voltage_range))
                
                # Update peak current
                peak_current_val = data_summary.get('peak_current', 0.0)
                if peak_current_val != 0.0:
                    current_text = f"{peak_current_val:.3f}A"
                    self.session_table.setItem(row, 5, QTableWidgetItem(current_text))
                
                # Update SOC
                latest_soc = data_summary.get('latest_soc')
                if latest_soc is not None:
                    soc_text = f"{latest_soc:.1f}%"
                    self.session_table.setItem(row, 6, QTableWidgetItem(soc_text))
                
                # Update cell delta max
                max_delta = data_summary.get('max_cell_delta', 0.0)
                if max_delta > 0.0:
                    delta_text = f"{max_delta:.3f}V"
                    self.session_table.setItem(row, 7, QTableWidgetItem(delta_text))
                
                break