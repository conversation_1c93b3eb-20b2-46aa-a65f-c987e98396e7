from datetime import datetime

def write_modbus_data_to_csv(data, filepath):
    """
    Write modbus data to CSV with specific formatting and headers
    Handles missing fields gracefully with default values
    """
    headers = [
        "Timestamp", "Cell1_V", "Cell2_V", "Cell3_V", "Cell4_V", "Cell5_V", "Cell6_V",
        "Cell7_V", "Cell8_V", "Pack_V", "Cell_Delta_V", "Temp1", "Temp2", "Current",
        "SOC", "FG_Voltage", "FG_Current", "FG_Temp", "Remaining_Cap", "Full_Cap",
        "Design_Cap", "Avg_Current", "Time_Empty", "Time_Full", "Internal_Temp",
        "Cycle_Count", "SOH", "Charging_V", "Charging_I", "Max_Temp", "Min_Temp",
        "Max_Chg_Curr", "Max_Dsg_Curr"
    ]

    def safe_get_voltage(key, default=0):
        """Safely get voltage value and convert to V, return default if missing"""
        try:
            return format(float(data.get(key, default)) * 0.001, '.3f')
        except (ValueError, TypeError):
            return format(float(default) * 0.001, '.3f')

    def safe_get(key, default=0):
        """Safely get value, return default if missing"""
        return data.get(key, default)

    values = [
        datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3],
        safe_get_voltage('afe_cell_volt1'),
        safe_get_voltage('afe_cell_volt2'),
        safe_get_voltage('afe_cell_volt3'),
        safe_get_voltage('afe_cell_volt4'),
        safe_get_voltage('afe_cell_volt5'),
        safe_get_voltage('afe_cell_volt6'),
        safe_get_voltage('afe_cell_volt7'),
        safe_get_voltage('afe_cell_volt8'),
        safe_get_voltage('afe_pack_volt'),
        safe_get_voltage('afe_cell_volt_delta'),
        safe_get('afe_temp1'),
        safe_get('afe_temp2'),
        safe_get('afe_current'),
        safe_get('fg_state_of_charge'),
        safe_get_voltage('fg_voltage'),
        safe_get('fg_current'),
        safe_get('fg_temperature'),
        safe_get('fg_remaining_capacity'),
        safe_get('fg_full_charge_cap'),
        safe_get('fg_design_capacity'),
        safe_get('fg_average_current'),
        safe_get('fg_time_to_empty'),
        safe_get('fg_time_to_full'),
        safe_get('fg_internal_temp'),
        safe_get('fg_cycle_count'),
        safe_get('fg_state_of_health'),
        safe_get_voltage('fg_charging_voltage'),
        safe_get('fg_charging_current'),
        safe_get('fg_lifetime_max_temp'),
        safe_get('fg_lifetime_min_temp'),
        safe_get('fg_lifetime_max_chg'),
        safe_get('fg_lifetime_max_dsg')
    ]

    import os
    write_header = not os.path.exists(filepath)

    with open(filepath, 'a', newline='') as f:
        if write_header:
            f.write(','.join(headers) + '\n')
        f.write(','.join(map(str, values)) + '\n')
