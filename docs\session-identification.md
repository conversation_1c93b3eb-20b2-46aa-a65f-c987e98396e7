# Session Identification Feature Implementation

## Overview

When clicking the "Refresh" button in the Session tab / page, analyze session CSV files to identify event types and calculate statistics.

## Implementation Todo List

### 1. Refactor src/app.py (Priority: High)
- [ ] Extract Session tab functionality to a separate module (e.g., `src/gui/session_tab.py`)
- [ ] Create a dedicated session analysis module (e.g., `src/core/session_analyzer.py`)
- [ ] Reduce app.py file size by modularizing components

### 2. Update Session History Table UI (Priority: High)
- [ ] Add new columns to the Session History table:
  - [ ] "Event" - Display session type (Charge/Discharge/Idle)
  - [ ] "Voltage Range" - Show voltage progression (e.g., "27.6V -> 28.6V")
  - [ ] "Current" - Display peak current value
  - [ ] "SOC" - Show State of Charge information
  - [ ] "Cell Delta Max" - Display maximum cell voltage delta

### 3. Implement Session Event Type Detection (Priority: High)
- [ ] Create function to analyze Current column in CSV files
- [ ] Implement logic to classify sessions:
  - If Current > 0.0 → "Charge" event
  - If Current < 0.0 → "Discharge" event  
  - If Current == 0.0 → "Idle" event
- [ ] Store session type in session metadata

### 4. Calculate Peak Current Statistics (Priority: Medium)
- [ ] For Charge events: Find maximum positive current value
- [ ] For Discharge events: Find minimum negative current value
- [ ] Display peak current in the "Current" column
- [ ] Format display (e.g., "1.363A" for charge, "-5.565A" for discharge)

### 5. Calculate Voltage Range Statistics (Priority: Medium)
- [ ] Read "Pack_V" column from CSV files
- [ ] For Charge/Discharge events:
  - [ ] Get starting Pack_V value (first row)
  - [ ] Get ending Pack_V value (last row)
  - [ ] Format as "start_V -> end_V" (e.g., "27.6V -> 28.6V")
- [ ] For Idle events: Show voltage range or average

### 6. Calculate Peak Cell Delta V (Priority: Medium)
- [ ] Analyze "Cell_Delta_V" column in CSV files
- [ ] Find maximum value across entire session
- [ ] Display in "Cell Delta Max" column with proper formatting

### 7. Update Refresh Button Handler (Priority: Medium)
- [ ] Modify refresh button to trigger session re-analysis
- [ ] Add progress indicator for analysis operation
- [ ] Handle errors gracefully (missing files, corrupted data)

### 8. Add SOC Statistics (Priority: Low)
- [ ] Determine SOC data source and column name
- [ ] Calculate SOC range or average for session
- [ ] Display in "SOC" column

## Technical Specifications

### Session Event Types
- **Charge**: Current > 0.0
- **Discharge**: Current < 0.0  
- **Idle**: Current == 0.0

### Data Format Examples
- **Peak Current**: 
  - Charging: "1.363A"
  - Discharging: "-5.565A"
- **Voltage Range**: "27.6V -> 28.6V"
- **Cell Delta Max**: "0.025V"

### Required CSV Columns
- `Current` - For event type detection and peak current
- `Pack_V` - For voltage range calculation
- `Cell_Delta_V` - For maximum cell delta calculation
- SOC column (name TBD) - For state of charge statistics

## Notes
- All calculations should be performed on demand when the Refresh button is clicked
- Consider caching results to improve performance for large CSV files
- Ensure proper error handling for missing or malformed data
