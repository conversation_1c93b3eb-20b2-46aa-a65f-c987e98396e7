#!/usr/bin/env python3
"""
GA Battery Management System - Real-time GUI Application
PyQt6 application for visualizing battery data via Modbus RTU communication
"""

import sys
import json
import csv
import os
import time
from datetime import datetime
from collections import deque
from typing import Dict, List, Optional
# Optional imports for CSV viewer functionality
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    pd = None
    PANDAS_AVAILABLE = False
try:
    import numpy as np
except ImportError:
    np = None

# PyQt6 imports
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QPushButton, QLabel, QTableWidget, QTableWidgetItem, QComboBox,
    QSpinBox, QGroupBox, QGridLayout, QStatusBar, QSplitter,
    QMessageBox, QFileDialog, QCheckBox, QTabWidget, QHeaderView,
    QDoubleSpinBox, QFormLayout, QListWidget, QListWidgetItem, QTextEdit, QScrollArea,
    QLineEdit
)
from PyQt6.QtCore import QTimer, QThread, pyqtSignal, Qt, QSettings
from PyQt6.QtGui import QFont, QPalette, QColor

# PyQtGraph for real-time plotting
import pyqtgraph as pg

# Serial and Modbus imports
import serial
import serial.tools.list_ports
from pymodbus.client import ModbusSerialClient
from pymodbus.exceptions import ModbusException

# Import existing modules
try:
    from .ga_modbus_csv_writer import write_modbus_data_to_csv
except ImportError:
    def write_modbus_data_to_csv(data, filename):
        """Fallback CSV writer"""
        pass

# Import session manager
try:
    from .core.session_manager import SessionManager
except ImportError:
    SessionManager = None

# Load battery specifications
try:
    with open('battery_pack_spec_json.json', 'r') as f:
        BATTERY_SPEC = json.load(f)
except FileNotFoundError:
    BATTERY_SPEC = {}

class ModbusWorker(QThread):
    """Worker thread for Modbus communication to prevent GUI blocking"""
    
    dataReceived = pyqtSignal(dict)
    statusChanged = pyqtSignal(str)
    errorOccurred = pyqtSignal(str)
    
    def __init__(self, session_manager=None):
        super().__init__()
        self.client = None
        self.running = False
        self.port = None
        self.baudrate = 9600
        self.parity = 'E'
        self.slave_id = 1  # From firmware source code
        self.interval = 1.0
        self.session_manager = session_manager
        
        # Register map based on existing code and battery spec - complete map
        self.register_map = {
            10: "afe_cell_volt1",
            11: "afe_cell_volt2",
            12: "afe_cell_volt3",
            13: "afe_cell_volt4",
            14: "afe_cell_volt5",
            15: "afe_cell_volt6",
            16: "afe_cell_volt7",
            17: "afe_cell_volt8",
            18: "afe_pack_volt",
            19: "afe_cell_volt_delta",
            20: "afe_temp1",
            21: "afe_temp2",
            22: "afe_current",
            23: "afe_adc_gain",
            24: "afe_adc_offset",
            25: "afe_ov_limit",
            26: "afe_uv_limit",
            27: "fg_state_of_charge",
            28: "fg_voltage",
            29: "fg_current",
            30: "fg_temperature",
            31: "fg_remaining_capacity",
            32: "fg_full_charge_cap",
            33: "fg_design_capacity",
            34: "fg_average_current",
            35: "fg_time_to_empty",
            36: "fg_time_to_full",
            37: "fg_internal_temp",
            38: "fg_cycle_count",
            39: "fg_state_of_health",
            40: "fg_charging_voltage",
            41: "fg_charging_current",
            42: "fg_lifetime_max_temp",
            43: "fg_lifetime_min_temp",
            44: "fg_lifetime_max_chg",
            45: "fg_lifetime_max_dsg"
        }
    
    def configure(self, port: str, baudrate: int = 9600, parity: str = 'E', 
                  slave_id: int = 2, interval: float = 1.0):
        """Configure Modbus connection parameters"""
        self.port = port
        self.baudrate = baudrate
        self.parity = parity
        self.slave_id = slave_id
        self.interval = interval
    
    def start_monitoring(self):
        """Start the monitoring thread"""
        self.running = True
        self.start()
    
    def stop_monitoring(self):
        """Stop the monitoring thread"""
        self.running = False
        if self.client and self.client.connected:
            self.client.close()
        self.wait()
    
    def run(self):
        """Main thread execution"""
        try:
            self.client = ModbusSerialClient(
                port=self.port,
                baudrate=self.baudrate,
                parity=self.parity,
                stopbits=1,
                bytesize=8,
                timeout=2,
                retries=3
            )
            
            if not self.client.connect():
                self.errorOccurred.emit(f"Failed to connect to {self.port}")
                return
            
            self.statusChanged.emit("Connected")
            
            while self.running:
                try:
                    # Read registers (start at address 9, read 30 registers)
                    response = self.client.read_input_registers(
                        address=9,
                        count=30,
                        slave=self.slave_id
                    )
                    
                    if not response.isError():
                        # Parse register values
                        values = response.registers
                        mapped_data = {}
                        
                        for i, value in enumerate(values):
                            register_address = 10 + i
                            if register_address in self.register_map:
                                param_name = self.register_map[register_address]
                                
                                # Apply scaling based on parameter type
                                if 'volt' in param_name and 'cell' in param_name:
                                    # Cell voltages in mV, convert to V
                                    scaled_value = value / 1000.0
                                elif param_name == 'afe_pack_volt':
                                    # Pack voltage in mV, convert to V  
                                    scaled_value = value / 1000.0
                                elif 'current' in param_name:
                                    # Current in mA, convert to A
                                    # Handle signed 16-bit values and filter false readings
                                    if value > 32767:  # Convert unsigned to signed 16-bit
                                        signed_value = value - 65536
                                    else:
                                        signed_value = value
                                    
                                    # Filter false readings near max uint16 values that indicate idle/zero current
                                    # Values like 65532, 65528 should be treated as 0 for idle battery
                                    if value >= 65500:  # Values close to max uint16 (65535) are false readings
                                        scaled_value = 0.0
                                    elif abs(signed_value) > 50000:  # Also filter if absolute current > 50A in mA
                                        scaled_value = 0.0
                                    else:
                                        scaled_value = signed_value / 1000.0
                                elif 'temp' in param_name:
                                    # Temperature conversion (if needed)
                                    scaled_value = value / 10.0  # Assuming 0.1°C resolution
                                else:
                                    scaled_value = value
                                
                                mapped_data[param_name] = scaled_value
                        
                        # Add timestamp
                        mapped_data['timestamp'] = datetime.now()
                        
                        self.dataReceived.emit(mapped_data)
                        
                    else:
                        self.errorOccurred.emit(f"Modbus read error: {response}")
                        
                    self.msleep(int(self.interval * 1000))
                    
                except ModbusException as e:
                    self.errorOccurred.emit(f"Modbus exception: {e}")
                    self.msleep(5000)  # Wait 5 seconds before retry
                except Exception as e:
                    self.errorOccurred.emit(f"Unexpected error: {e}")
                    self.msleep(5000)
                    
        except Exception as e:
            self.errorOccurred.emit(f"Connection error: {e}")
        finally:
            if self.client and self.client.connected:
                self.client.close()
            self.statusChanged.emit("Disconnected")


class RealTimePlotWidget(QWidget):
    """Real-time plotting widget using PyQtGraph"""
    
    def __init__(self, title: str, y_label: str, line_colors: List[str] = None):
        super().__init__()
        self.title = title
        self.y_label = y_label
        self.line_colors = line_colors or ['#ff0000', '#00ff00', '#0000ff', '#ffff00', 
                                          '#ff00ff', '#00ffff', '#ffa500', '#800080']
        
        # Data storage (last 300 points = 5 minutes at 1Hz)
        self.max_points = 300
        self.time_data = deque(maxlen=self.max_points)
        self.plot_data = {}
        self.plot_lines = {}
        self.current_theme = "light"
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the plot widget UI"""
        layout = QVBoxLayout()
        
        # Create plot widget
        self.plot_widget = pg.PlotWidget(title=self.title)
        self.plot_widget.setLabel('left', self.y_label)
        self.plot_widget.setLabel('bottom', 'Time (s)')
        self.plot_widget.showGrid(True, True)
        
        # Apply initial theme
        self.apply_theme(self.current_theme)
        
        # Enable auto-scaling
        self.plot_widget.enableAutoRange()
        
        layout.addWidget(self.plot_widget)
        self.setLayout(layout)
    
    def add_data_series(self, name: str, color: str = None):
        """Add a new data series to the plot"""
        if color is None:
            color = self.line_colors[len(self.plot_lines) % len(self.line_colors)]
        
        self.plot_data[name] = deque(maxlen=self.max_points)
        pen = pg.mkPen(color=color, width=2)
        self.plot_lines[name] = self.plot_widget.plot([], [], pen=pen, name=name)
    
    def update_data(self, name: str, value: float, timestamp: datetime):
        """Update data for a specific series"""
        if name not in self.plot_data:
            self.add_data_series(name)

        # Calculate time offset from first data point
        if not self.time_data:
            self.start_time = timestamp

        time_offset = (timestamp - self.start_time).total_seconds()

        # Only append time data if this is the first series or if time_data is shorter than expected
        if len(self.time_data) == 0 or len(self.time_data) <= len(self.plot_data[name]):
            self.time_data.append(time_offset)

        self.plot_data[name].append(value)

        # Ensure time_data and plot_data have the same length for this series
        time_data_for_series = list(self.time_data)[-len(self.plot_data[name]):]

        # Update the plot line
        self.plot_lines[name].setData(time_data_for_series, list(self.plot_data[name]))
    
    def apply_theme(self, theme: str):
        """Apply theme to the plot widget"""
        self.current_theme = theme
        
        if theme == "dark":
            # Dark theme
            self.plot_widget.setBackground('#2b2b2b')
            self.plot_widget.getAxis('left').setPen('#ffffff')
            self.plot_widget.getAxis('bottom').setPen('#ffffff')
            self.plot_widget.getAxis('left').setTextPen('#ffffff')
            self.plot_widget.getAxis('bottom').setTextPen('#ffffff')
            # Update grid color
            self.plot_widget.showGrid(x=True, y=True, alpha=0.3)
        elif theme == "light":
            # Light theme
            self.plot_widget.setBackground('#ffffff')
            self.plot_widget.getAxis('left').setPen('#000000')
            self.plot_widget.getAxis('bottom').setPen('#000000')
            self.plot_widget.getAxis('left').setTextPen('#000000')
            self.plot_widget.getAxis('bottom').setTextPen('#000000')
            # Update grid color
            self.plot_widget.showGrid(x=True, y=True, alpha=0.3)
        else:  # system
            # Use system theme colors
            palette = QApplication.palette()
            bg_color = palette.color(QPalette.ColorRole.Base)
            text_color = palette.color(QPalette.ColorRole.Text)
            
            self.plot_widget.setBackground(bg_color)
            self.plot_widget.getAxis('left').setPen(text_color)
            self.plot_widget.getAxis('bottom').setPen(text_color)
            self.plot_widget.getAxis('left').setTextPen(text_color)
            self.plot_widget.getAxis('bottom').setTextPen(text_color)
            self.plot_widget.showGrid(x=True, y=True, alpha=0.3)


class DataTableWidget(QWidget):
    """Widget for displaying current data values in a table"""

    # Threshold for cell voltage delta in mV (adjust as needed)
    CELL_DELTA_THRESHOLD_MV = 50.0

    def __init__(self):
        super().__init__()
        self.data_rows = {}
        self.settings_widget = None  # Will be set by main window
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the table widget UI"""
        layout = QVBoxLayout()
        
        # Create table
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(['Parameter', 'Value', 'Unit'])
        self.table.horizontalHeader().setStretchLastSection(True)
        
        layout.addWidget(self.table)
        self.setLayout(layout)
    
    def set_settings_widget(self, settings_widget):
        """Set reference to settings widget for threshold access"""
        self.settings_widget = settings_widget
    
    def calculate_cell_voltage_delta(self, data: Dict[str, float]) -> float:
        """Calculate the delta between highest and lowest cell voltages in mV"""
        cell_voltages = []
        for i in range(1, 9):  # Cells 1-8
            param = f"afe_cell_volt{i}"
            if param in data:
                cell_voltages.append(data[param])
        
        if len(cell_voltages) >= 2:
            return (max(cell_voltages) - min(cell_voltages)) * 1000  # Convert V to mV
        return 0.0
    
    def get_color_for_value(self, param: str, value: float, data: Dict[str, float]) -> QColor:
        """Determine color for a parameter value based on thresholds"""

        # Cell voltage delta color logic (independent of settings widget)
        if param == 'afe_cell_volt_delta':
            if value > self.CELL_DELTA_THRESHOLD_MV:
                return QColor(255, 0, 0)  # Red for exceeding threshold
            else:
                return QColor(0, 128, 0)  # Green for within threshold

        # For other parameters, check if settings widget is available
        if not self.settings_widget:
            return QColor(0, 0, 0)  # Default black

        # Cell voltage color logic
        if 'afe_cell_volt' in param and param != 'afe_cell_volt_delta':
            cell_delta = self.calculate_cell_voltage_delta(data)
            delta_threshold = self.settings_widget.get_cell_delta_threshold()

            if cell_delta > delta_threshold:
                return QColor(255, 0, 0)  # Red for warning
            else:
                return QColor(0, 128, 0)  # Green for normal

        # Pack voltage color logic
        elif param == 'afe_pack_volt':
            min_threshold = self.settings_widget.get_pack_volt_min()
            max_threshold = self.settings_widget.get_pack_volt_max()

            if value < min_threshold or value > max_threshold:
                return QColor(255, 0, 0)  # Red for out of range
            else:
                return QColor(0, 128, 0)  # Green for in range

        # Default color for other parameters
        return QColor(0, 0, 0)  # Black
    
    def update_data(self, data: Dict[str, float]):
        """Update table with new data"""
        current_row = 0
        
        for param, value in data.items():
            if param == 'timestamp':
                continue
                
            # Determine unit based on parameter name
            if 'volt' in param:
                unit = 'V'
                formatted_value = f"{value:.3f}"
            elif 'current' in param:
                unit = 'A' 
                formatted_value = f"{value:.3f}"
            elif 'temp' in param:
                unit = '°C'
                formatted_value = f"{value:.1f}"
            elif 'charge' in param:
                unit = '%'
                formatted_value = f"{value:.1f}"
            elif 'capacity' in param:
                unit = 'Ah'
                formatted_value = f"{value:.2f}"
            elif 'cycle' in param:
                unit = 'cycles'
                formatted_value = f"{int(value)}"
            else:
                unit = ''
                formatted_value = f"{value}"
            
            # Add or update row
            if param not in self.data_rows:
                self.table.setRowCount(current_row + 1)
                self.data_rows[param] = current_row
                
                # Set parameter name
                param_item = QTableWidgetItem(param.replace('_', ' ').title())
                self.table.setItem(current_row, 0, param_item)
                
                # Set unit
                unit_item = QTableWidgetItem(unit)
                self.table.setItem(current_row, 2, unit_item)
                
                current_row += 1
            
            # Update value with color coding
            row = self.data_rows[param]
            value_item = QTableWidgetItem(formatted_value)
            
            # Apply color based on thresholds
            color = self.get_color_for_value(param, value, data)
            value_item.setForeground(color)
            
            self.table.setItem(row, 1, value_item)


class CSVLogTableWidget(QWidget):
    """Widget for displaying CSV logging data in real-time"""
    
    def __init__(self, max_rows: int = 100):
        super().__init__()
        self.max_rows = max_rows
        self.log_data = []
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the CSV log table widget UI"""
        layout = QVBoxLayout()
        
        # Create table
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSortingEnabled(True)
        
        # Headers will be set when first data arrives
        self.headers_set = False
        
        layout.addWidget(self.table)
        self.setLayout(layout)
    
    def update_data(self, data: Dict[str, float]):
        """Add new row of data to the CSV log table"""
        # Convert data to row format
        timestamp = data.get('timestamp', datetime.now())
        
        # Create row data with timestamp first
        row_data = {'Timestamp': timestamp.strftime('%Y-%m-%d %H:%M:%S')}
        
        # Add all other parameters
        for param, value in data.items():
            if param == 'timestamp':
                continue
                
            # Format value based on parameter type
            if 'volt' in param:
                formatted_value = f"{value:.3f}"
            elif 'current' in param:
                formatted_value = f"{value:.3f}"
            elif 'temp' in param:
                formatted_value = f"{value:.1f}"
            elif 'charge' in param:
                formatted_value = f"{value:.1f}"
            elif 'capacity' in param:
                formatted_value = f"{value:.2f}"
            elif 'cycle' in param:
                formatted_value = f"{int(value)}"
            else:
                formatted_value = f"{value}"
            
            # Clean up parameter name for display
            display_name = param.replace('_', ' ').title()
            row_data[display_name] = formatted_value
        
        # Set headers on first data
        if not self.headers_set:
            headers = list(row_data.keys())
            self.table.setColumnCount(len(headers))
            self.table.setHorizontalHeaderLabels(headers)
            self.headers_set = True
            
            # Auto-resize columns
            self.table.horizontalHeader().setStretchLastSection(True)
            for i in range(len(headers) - 1):
                self.table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)
        
        # Add row to table
        self.log_data.append(row_data)
        
        # Limit number of rows
        if len(self.log_data) > self.max_rows:
            self.log_data.pop(0)
        
        # Update table
        self.refresh_table()
    
    def refresh_table(self):
        """Refresh the entire table display"""
        if not self.log_data:
            return
            
        self.table.setRowCount(len(self.log_data))
        
        # Get headers
        headers = list(self.log_data[0].keys()) if self.log_data else []
        
        # Populate table
        for row_idx, row_data in enumerate(self.log_data):
            for col_idx, header in enumerate(headers):
                value = row_data.get(header, '')
                item = QTableWidgetItem(str(value))
                self.table.setItem(row_idx, col_idx, item)
        
        # Scroll to bottom to show latest data
        self.table.scrollToBottom()
    
    def clear_data(self):
        """Clear all logged data"""
        self.log_data.clear()
        self.table.setRowCount(0)
        self.headers_set = False


class CSVViewerWidget(QWidget):
    """Widget for viewing and analyzing CSV files"""
    
    def __init__(self):
        super().__init__()
        self.current_csv_data = None
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the CSV viewer UI"""
        layout = QHBoxLayout()
        
        # Left panel: Directory browser and file list
        left_panel = QWidget()
        left_layout = QVBoxLayout()
        left_panel.setLayout(left_layout)
        left_panel.setMaximumWidth(300)
        
        # Directory controls
        dir_controls = QVBoxLayout()
        
        # Breadcrumb navigation
        breadcrumb_layout = QHBoxLayout()
        self.dir_label = QLabel("Directory: log/")
        breadcrumb_layout.addWidget(self.dir_label)
        
        browse_btn = QPushButton("Browse...")
        browse_btn.clicked.connect(self.browse_directory)
        breadcrumb_layout.addWidget(browse_btn)
        
        dir_controls.addLayout(breadcrumb_layout)
        
        # Navigation buttons
        nav_layout = QHBoxLayout()
        self.back_btn = QPushButton("↑ Parent")
        self.back_btn.clicked.connect(self.navigate_to_parent)
        self.back_btn.setEnabled(False)
        nav_layout.addWidget(self.back_btn)
        
        self.home_btn = QPushButton("🏠 Log Root")
        self.home_btn.clicked.connect(self.navigate_to_log_root)
        nav_layout.addWidget(self.home_btn)
        nav_layout.addStretch()
        
        dir_controls.addLayout(nav_layout)
        
        left_layout.addLayout(dir_controls)
        
        # File list
        self.file_list = QListWidget()
        self.file_list.itemDoubleClicked.connect(self.load_selected_file)
        left_layout.addWidget(QLabel("CSV Files:"))
        left_layout.addWidget(self.file_list)
        
        # Load button
        load_btn = QPushButton("Load Selected File")
        load_btn.clicked.connect(self.load_selected_file)
        left_layout.addWidget(load_btn)
        
        layout.addWidget(left_panel)
        
        # Right panel: Chart, data table, and statistics
        right_panel = QWidget()
        right_layout = QVBoxLayout()
        right_panel.setLayout(right_layout)
        
        # Chart area
        chart_group = QGroupBox("Data Visualization")
        chart_layout = QVBoxLayout()
        
        # Chart controls
        chart_controls = QHBoxLayout()
        chart_controls.addWidget(QLabel("Y-axis:"))
        self.y_axis_combo = QComboBox()
        chart_controls.addWidget(self.y_axis_combo)
        
        self.multi_series_checkbox = QCheckBox("Show all cell voltages")
        self.multi_series_checkbox.stateChanged.connect(self.update_chart)
        chart_controls.addWidget(self.multi_series_checkbox)
        
        chart_controls.addStretch()
        chart_layout.addLayout(chart_controls)
        
        # Plot widget
        self.csv_plot = RealTimePlotWidget("CSV Data Visualization", "Value")
        chart_layout.addWidget(self.csv_plot)
        
        chart_group.setLayout(chart_layout)
        right_layout.addWidget(chart_group)
        
        # Data table and statistics in splitter
        data_splitter = QSplitter(Qt.Orientation.Vertical)
        
        # Data table
        table_group = QGroupBox("Data Table")
        table_layout = QVBoxLayout()
        
        self.csv_table = QTableWidget()
        self.csv_table.setAlternatingRowColors(True)
        self.csv_table.setSortingEnabled(True)
        table_layout.addWidget(self.csv_table)
        
        table_group.setLayout(table_layout)
        data_splitter.addWidget(table_group)
        
        # Statistics panel
        stats_group = QGroupBox("Statistics")
        stats_layout = QVBoxLayout()
        
        self.stats_text = QTextEdit()
        self.stats_text.setMaximumHeight(200)
        self.stats_text.setReadOnly(True)
        stats_layout.addWidget(self.stats_text)
        
        stats_group.setLayout(stats_layout)
        data_splitter.addWidget(stats_group)
        
        data_splitter.setSizes([400, 200])
        right_layout.addWidget(data_splitter)
        
        layout.addWidget(right_panel)
        
        self.setLayout(layout)
        
        # Initialize with log directory
        self.current_directory = "log"
        self.update_navigation_state()
        self.refresh_file_list()
    
    def browse_directory(self):
        """Browse for a directory containing CSV files"""
        directory = QFileDialog.getExistingDirectory(
            self, "Select Directory", self.current_directory
        )
        if directory:
            self.current_directory = directory
            self.update_navigation_state()
            self.refresh_file_list()
    
    def refresh_file_list(self):
        """Refresh the list of directories and CSV files in the current directory"""
        self.file_list.clear()
        
        if not os.path.exists(self.current_directory):
            os.makedirs(self.current_directory, exist_ok=True)
        
        try:
            # Get all items in directory
            all_items = os.listdir(self.current_directory)
            
            # Separate directories and CSV files
            directories = []
            csv_files = []
            
            for item in all_items:
                item_path = os.path.join(self.current_directory, item)
                if os.path.isdir(item_path):
                    directories.append(item)
                elif item.endswith('.csv'):
                    csv_files.append(item)
            
            # Sort directories and files
            directories.sort()
            csv_files.sort(reverse=True)  # Newest CSV files first
            
            # Add parent directory option if not in root log directory
            if self.current_directory != "log" and "log" in self.current_directory:
                parent_item = QListWidgetItem("📁 .. (Parent Directory)")
                parent_item.setData(Qt.ItemDataRole.UserRole, "..")
                self.file_list.addItem(parent_item)
            
            # Add directories with folder icon
            for directory in directories:
                dir_item = QListWidgetItem(f"📁 {directory}")
                dir_item.setData(Qt.ItemDataRole.UserRole, directory)
                self.file_list.addItem(dir_item)
            
            # Add CSV files with document icon
            for file in csv_files:
                file_item = QListWidgetItem(f"📄 {file}")
                file_item.setData(Qt.ItemDataRole.UserRole, file)
                self.file_list.addItem(file_item)
                
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to read directory: {e}")
    
    def load_selected_file(self):
        """Load the selected CSV file or navigate to selected directory"""
        current_item = self.file_list.currentItem()
        if not current_item:
            QMessageBox.information(self, "Info", "Please select a file or directory first.")
            return
        
        item_name = current_item.data(Qt.ItemDataRole.UserRole)
        
        # Handle directory navigation
        if current_item.text().startswith("📁"):
            if item_name == "..":
                # Navigate to parent directory
                parent_dir = os.path.dirname(self.current_directory)
                if parent_dir and parent_dir != self.current_directory:
                    self.current_directory = parent_dir
                    self.update_navigation_state()
                    self.refresh_file_list()
            else:
                # Navigate into subdirectory
                new_dir = os.path.join(self.current_directory, item_name)
                if os.path.isdir(new_dir):
                    self.current_directory = new_dir
                    self.update_navigation_state()
                    self.refresh_file_list()
            return
        
        # Handle CSV file loading
        if not current_item.text().startswith("📄"):
            QMessageBox.information(self, "Info", "Please select a CSV file to load.")
            return
            
        # Check if pandas is available
        if not PANDAS_AVAILABLE:
            QMessageBox.warning(self, "Missing Dependencies", 
                              "CSV Viewer requires pandas and numpy.\n\n"
                              "Install with: pip install pandas numpy")
            return
        
        filepath = os.path.join(self.current_directory, item_name)
        
        try:
            # Load CSV data with pandas
            self.current_csv_data = pd.read_csv(filepath)
            
            # Update UI components
            self.populate_data_table()
            self.populate_y_axis_combo()
            self.update_chart()
            self.update_statistics()
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load CSV file: {e}")
    
    def populate_data_table(self):
        """Populate the data table with CSV data"""
        if self.current_csv_data is None:
            return
        
        # Set up table
        self.csv_table.setRowCount(len(self.current_csv_data))
        self.csv_table.setColumnCount(len(self.current_csv_data.columns))
        self.csv_table.setHorizontalHeaderLabels(self.current_csv_data.columns.tolist())
        
        # Populate data
        for row in range(len(self.current_csv_data)):
            for col in range(len(self.current_csv_data.columns)):
                value = self.current_csv_data.iloc[row, col]
                item = QTableWidgetItem(str(value))
                self.csv_table.setItem(row, col, item)
        
        # Auto-resize columns
        self.csv_table.horizontalHeader().setStretchLastSection(True)
        for i in range(len(self.current_csv_data.columns) - 1):
            self.csv_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)
    
    def populate_y_axis_combo(self):
        """Populate the Y-axis selection combo box"""
        self.y_axis_combo.clear()
        
        if self.current_csv_data is None:
            return
        
        # Add numeric columns to combo box, prioritizing important battery parameters
        numeric_columns = self.current_csv_data.select_dtypes(include=[np.number]).columns.tolist()
        
        # Sort columns to put important battery parameters first
        priority_order = []
        other_columns = []
        
        for col in numeric_columns:
            col_lower = col.lower()
            if ('pack' in col_lower and 'v' in col_lower) or col_lower == 'pack_v':
                priority_order.insert(0, col)  # Pack voltage first
            elif 'current' in col_lower:
                priority_order.append(col)  # Current second
            elif ('cell' in col_lower and 'v' in col_lower) or 'cell_volt' in col_lower:
                priority_order.append(col)  # Cell voltages next
            elif 'soc' in col_lower:
                priority_order.append(col)  # SOC next
            elif 'temp' in col_lower:
                priority_order.append(col)  # Temperature next
            else:
                other_columns.append(col)
        
        # Add all columns in priority order
        for col in priority_order + other_columns:
            self.y_axis_combo.addItem(col)
        
        # Connect signal after populating
        self.y_axis_combo.currentTextChanged.connect(self.update_chart)
    
    def update_chart(self):
        """Update the chart with selected data"""
        if self.current_csv_data is None:
            return
        
        # Clear existing plots
        self.csv_plot.plot_widget.clear()
        self.csv_plot.plot_data.clear()
        self.csv_plot.plot_lines.clear()
        self.csv_plot.time_data.clear()
        
        # Get time column (assume first column or look for timestamp)
        time_col = None
        if 'timestamp' in self.current_csv_data.columns:
            time_col = 'timestamp'
        elif 'time' in self.current_csv_data.columns:
            time_col = 'time'
        else:
            # Use index as time
            time_data = list(range(len(self.current_csv_data)))
        
        if time_col:
            try:
                time_data = pd.to_datetime(self.current_csv_data[time_col])
                time_data = [(t - time_data.iloc[0]).total_seconds() for t in time_data]
            except:
                time_data = list(range(len(self.current_csv_data)))
        
        if self.multi_series_checkbox.isChecked():
            # Show all cell voltages - check for various naming patterns
            cell_cols = [col for col in self.current_csv_data.columns 
                        if 'cell_volt' in col.lower() or 'cell volt' in col.lower() or 
                        (col.lower().startswith('cell') and '_v' in col.lower())]
            
            for i, col in enumerate(cell_cols):
                if col in self.current_csv_data.columns:
                    y_data = self.current_csv_data[col].dropna()
                    x_data = time_data[:len(y_data)]
                    
                    color = self.csv_plot.line_colors[i % len(self.csv_plot.line_colors)]
                    pen = pg.mkPen(color=color, width=2)
                    self.csv_plot.plot_widget.plot(x_data, y_data, pen=pen, name=col)
        else:
            # Show selected Y-axis data
            selected_col = self.y_axis_combo.currentText()
            if selected_col and selected_col in self.current_csv_data.columns:
                y_data = self.current_csv_data[selected_col].dropna()
                x_data = time_data[:len(y_data)]
                
                pen = pg.mkPen(color='#0066cc', width=2)
                self.csv_plot.plot_widget.plot(x_data, y_data, pen=pen, name=selected_col)
        
        # Update plot labels
        self.csv_plot.plot_widget.setLabel('left', 'Value')
        self.csv_plot.plot_widget.setLabel('bottom', 'Time (s)' if time_col else 'Sample')
    
    def update_statistics(self):
        """Update the statistics display"""
        if self.current_csv_data is None:
            self.stats_text.clear()
            return
        
        stats_html = "<h3>Dataset Statistics</h3>"
        stats_html += f"<b>Rows:</b> {len(self.current_csv_data)}<br>"
        stats_html += f"<b>Columns:</b> {len(self.current_csv_data.columns)}<br><br>"
        
        # Numeric column statistics
        numeric_cols = self.current_csv_data.select_dtypes(include=[np.number])
        
        if not numeric_cols.empty:
            stats_html += "<h4>Numeric Columns:</h4>"
            stats_html += "<table border='1' cellpadding='3' cellspacing='0'>"
            stats_html += "<tr><th>Column</th><th>Mean</th><th>Min</th><th>Max</th><th>Std Dev</th></tr>"
            
            for col in numeric_cols.columns:
                data = numeric_cols[col].dropna()
                if len(data) > 0:
                    stats_html += f"<tr>"
                    stats_html += f"<td>{col}</td>"
                    stats_html += f"<td>{data.mean():.3f}</td>"
                    stats_html += f"<td>{data.min():.3f}</td>"
                    stats_html += f"<td>{data.max():.3f}</td>"
                    stats_html += f"<td>{data.std():.3f}</td>"
                    stats_html += f"</tr>"
            
            stats_html += "</table><br>"
        
        # Cell voltage analysis if available - check for various naming patterns
        cell_cols = [col for col in self.current_csv_data.columns 
                    if 'cell_volt' in col.lower() or 'cell volt' in col.lower() or 
                    (col.lower().startswith('cell') and '_v' in col.lower())]
        
        if cell_cols:
            stats_html += "<h4>Cell Voltage Analysis:</h4>"
            cell_data = self.current_csv_data[cell_cols].dropna()
            
            if not cell_data.empty:
                # Calculate cell balance metrics
                cell_min = cell_data.min(axis=1)
                cell_max = cell_data.max(axis=1)
                cell_delta = (cell_max - cell_min) * 1000  # Convert to mV
                
                stats_html += f"<b>Average Cell Delta:</b> {cell_delta.mean():.1f} mV<br>"
                stats_html += f"<b>Max Cell Delta:</b> {cell_delta.max():.1f} mV<br>"
                stats_html += f"<b>Min Cell Voltage:</b> {cell_data.min().min():.3f} V<br>"
                stats_html += f"<b>Max Cell Voltage:</b> {cell_data.max().max():.3f} V<br>"
        
        self.stats_text.setHtml(stats_html)
    
    def update_navigation_state(self):
        """Update navigation buttons and directory label"""
        # Update directory label with full path relative to project root
        if self.current_directory == "log":
            display_path = "log/"
            self.back_btn.setEnabled(False)
        else:
            # Show relative path from log directory
            if self.current_directory.startswith("log/"):
                display_path = self.current_directory + "/"
            else:
                display_path = os.path.basename(self.current_directory) + "/"
            self.back_btn.setEnabled(True)
        
        self.dir_label.setText(f"Directory: {display_path}")
    
    def navigate_to_parent(self):
        """Navigate to parent directory"""
        parent_dir = os.path.dirname(self.current_directory)
        if parent_dir and parent_dir != self.current_directory:
            self.current_directory = parent_dir
            self.update_navigation_state()
            self.refresh_file_list()
    
    def navigate_to_log_root(self):
        """Navigate to log root directory"""
        self.current_directory = "log"
        self.update_navigation_state()
        self.refresh_file_list()


class CommandsWidget(QWidget):
    """Widget for executing Modbus fuel gauge commands"""
    
    # Command response signal
    commandResponse = pyqtSignal(str, str)  # command_name, response
    
    def __init__(self, modbus_worker):
        super().__init__()
        self.modbus_worker = modbus_worker
        self.pending_commands = {}  # Track pending command responses
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the commands widget UI"""
        layout = QVBoxLayout()
        
        # Header with warning
        header_layout = QVBoxLayout()
        
        title_label = QLabel("<h2>⚠️  Fuel Gauge Commands</h2>")
        header_layout.addWidget(title_label)
        
        # Connection status indicator
        self.connection_status_label = QLabel("🔴 Not Connected - Start monitoring to enable commands")
        self.connection_status_label.setStyleSheet("color: #d63031; background-color: #fff5f5; padding: 8px; border: 1px solid #fab1a0; border-radius: 5px; font-weight: bold;")
        header_layout.addWidget(self.connection_status_label)
        
        warning_label = QLabel(
            "<b>WARNING:</b> These commands modify fuel gauge settings and calibration. "
            "Use with extreme caution and only if you understand the consequences. "
            "Some commands may permanently alter battery behavior."
        )
        warning_label.setStyleSheet("color: #d63031; background-color: #fff5f5; padding: 10px; border: 1px solid #fab1a0; border-radius: 5px;")
        warning_label.setWordWrap(True)
        header_layout.addWidget(warning_label)
        
        layout.addLayout(header_layout)
        
        # Main content in horizontal splitter for better space utilization
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left side - Command groups (scrollable)
        left_widget = QWidget()
        left_widget.setMinimumWidth(650)  # Ensure adequate width for command groups
        
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout()
        scroll_widget.setLayout(scroll_layout)
        
        # FET Control Group
        fet_group = QGroupBox("FET Control")
        fet_layout = QGridLayout()
        fet_layout.setSpacing(10)
        
        self.charge_fet_on_btn = QPushButton("Charge FET ON")
        self.charge_fet_on_btn.setMinimumHeight(40)
        self.charge_fet_on_btn.clicked.connect(lambda: self.send_command("charge_fet_on", 1))
        fet_layout.addWidget(self.charge_fet_on_btn, 0, 0)
        
        self.charge_fet_off_btn = QPushButton("Charge FET OFF")
        self.charge_fet_off_btn.setMinimumHeight(40)
        self.charge_fet_off_btn.clicked.connect(lambda: self.send_command("charge_fet_off", 0))
        fet_layout.addWidget(self.charge_fet_off_btn, 0, 1)
        
        self.discharge_fet_on_btn = QPushButton("Discharge FET ON")
        self.discharge_fet_on_btn.setMinimumHeight(40)
        self.discharge_fet_on_btn.clicked.connect(lambda: self.send_command("discharge_fet_on", 1))
        fet_layout.addWidget(self.discharge_fet_on_btn, 1, 0)
        
        self.discharge_fet_off_btn = QPushButton("Discharge FET OFF")
        self.discharge_fet_off_btn.setMinimumHeight(40)
        self.discharge_fet_off_btn.clicked.connect(lambda: self.send_command("discharge_fet_off", 0))
        fet_layout.addWidget(self.discharge_fet_off_btn, 1, 1)
        
        fet_group.setLayout(fet_layout)
        scroll_layout.addWidget(fet_group)
        
        # System Control Group
        system_group = QGroupBox("System Control")
        system_layout = QGridLayout()
        system_layout.setSpacing(10)
        
        self.reset_micro_btn = QPushButton("Reset Microcontroller")
        self.reset_micro_btn.setMinimumHeight(40)
        self.reset_micro_btn.clicked.connect(lambda: self.send_command("reset_micro"))
        self.reset_micro_btn.setStyleSheet("background-color: #e17055;")
        system_layout.addWidget(self.reset_micro_btn, 0, 0)
        
        self.fg_reset_btn = QPushButton("Reset Fuel Gauge")
        self.fg_reset_btn.setMinimumHeight(40)
        self.fg_reset_btn.clicked.connect(lambda: self.send_command("fg_reset"))
        self.fg_reset_btn.setStyleSheet("background-color: #e17055;")
        system_layout.addWidget(self.fg_reset_btn, 0, 1)
        
        self.rebalance_on_btn = QPushButton("Enter Rebalance Mode")
        self.rebalance_on_btn.setMinimumHeight(40)
        self.rebalance_on_btn.clicked.connect(lambda: self.send_command("rebalance_mode", 1))
        system_layout.addWidget(self.rebalance_on_btn, 1, 0)
        
        self.rebalance_off_btn = QPushButton("Exit Rebalance Mode")
        self.rebalance_off_btn.setMinimumHeight(40)
        self.rebalance_off_btn.clicked.connect(lambda: self.send_command("rebalance_mode", 0))
        system_layout.addWidget(self.rebalance_off_btn, 1, 1)
        
        system_group.setLayout(system_layout)
        scroll_layout.addWidget(system_group)
        
        # Fuel Gauge Security Group
        security_group = QGroupBox("Fuel Gauge Security")
        security_layout = QGridLayout()
        security_layout.setSpacing(10)
        
        # Unseal key input
        security_layout.addWidget(QLabel("Unseal Key (hex):"), 0, 0)
        self.unseal_key_input = QLineEdit()
        self.unseal_key_input.setText("15038901")
        self.unseal_key_input.setPlaceholderText("e.g., 15038901")
        self.unseal_key_input.setMinimumHeight(30)
        security_layout.addWidget(self.unseal_key_input, 0, 1)
        
        self.set_unseal_key_btn = QPushButton("Set Unseal Key")
        self.set_unseal_key_btn.setMinimumHeight(35)
        self.set_unseal_key_btn.clicked.connect(self.set_unseal_key)
        security_layout.addWidget(self.set_unseal_key_btn, 0, 2)
        
        self.unseal_btn = QPushButton("Unseal Fuel Gauge")
        self.unseal_btn.setMinimumHeight(35)
        self.unseal_btn.clicked.connect(lambda: self.send_command("fg_unseal"))
        security_layout.addWidget(self.unseal_btn, 1, 0)
        
        self.seal_btn = QPushButton("Seal Fuel Gauge")
        self.seal_btn.setMinimumHeight(35)
        self.seal_btn.clicked.connect(lambda: self.send_command("fg_seal"))
        security_layout.addWidget(self.seal_btn, 1, 1)
        
        # Full unseal key input
        security_layout.addWidget(QLabel("Full Unseal Key (hex):"), 2, 0)
        self.full_unseal_key_input = QLineEdit()
        self.full_unseal_key_input.setText("1503ABCD")
        self.full_unseal_key_input.setPlaceholderText("e.g., 1503ABCD")
        self.full_unseal_key_input.setMinimumHeight(30)
        security_layout.addWidget(self.full_unseal_key_input, 2, 1)
        
        self.set_full_unseal_key_btn = QPushButton("Set Full Unseal Key")
        self.set_full_unseal_key_btn.setMinimumHeight(35)
        self.set_full_unseal_key_btn.clicked.connect(self.set_full_unseal_key)
        security_layout.addWidget(self.set_full_unseal_key_btn, 2, 2)
        
        self.full_unseal_btn = QPushButton("Full Unseal Fuel Gauge")
        self.full_unseal_btn.setMinimumHeight(35)
        self.full_unseal_btn.clicked.connect(lambda: self.send_command("fg_full_unseal"))
        security_layout.addWidget(self.full_unseal_btn, 3, 0)
        
        # Debug button to check unseal status
        self.check_unseal_status_btn = QPushButton("Check Unseal Status")
        self.check_unseal_status_btn.setMinimumHeight(35)
        self.check_unseal_status_btn.clicked.connect(self.check_unseal_status)
        self.check_unseal_status_btn.setStyleSheet("background-color: #74b9ff;")
        security_layout.addWidget(self.check_unseal_status_btn, 3, 2)
        
        self.fg_lt_enable_btn = QPushButton("FG Lifetime Enable")
        self.fg_lt_enable_btn.setMinimumHeight(35)
        self.fg_lt_enable_btn.clicked.connect(lambda: self.send_command("fg_lt_enable"))
        security_layout.addWidget(self.fg_lt_enable_btn, 3, 1)
        
        self.program_fg_btn = QPushButton("Program Fuel Gauge")
        self.program_fg_btn.setMinimumHeight(35)
        self.program_fg_btn.clicked.connect(lambda: self.send_command("fg_program"))
        self.program_fg_btn.setStyleSheet("background-color: #e17055;")
        security_layout.addWidget(self.program_fg_btn, 4, 0)
        
        # Debug button to test holding registers
        self.test_registers_btn = QPushButton("Test Holding Registers")
        self.test_registers_btn.setMinimumHeight(35)
        self.test_registers_btn.clicked.connect(self.test_holding_registers)
        self.test_registers_btn.setStyleSheet("background-color: #fd79a8;")
        security_layout.addWidget(self.test_registers_btn, 4, 1)
        
        security_group.setLayout(security_layout)
        scroll_layout.addWidget(security_group)
        
        # Calibration Group
        calibration_group = QGroupBox("Fuel Gauge Calibration")
        calibration_layout = QGridLayout()
        calibration_layout.setSpacing(10)
        
        # Voltage calibration
        calibration_layout.addWidget(QLabel("Applied Voltage (V):"), 0, 0)
        self.applied_voltage_input = QDoubleSpinBox()
        self.applied_voltage_input.setRange(24.0, 28.4)
        self.applied_voltage_input.setValue(26.4)
        self.applied_voltage_input.setSingleStep(0.1)
        self.applied_voltage_input.setDecimals(1)
        self.applied_voltage_input.setMinimumHeight(30)
        calibration_layout.addWidget(self.applied_voltage_input, 0, 1)
        
        self.set_applied_voltage_btn = QPushButton("Set Applied Voltage")
        self.set_applied_voltage_btn.setMinimumHeight(35)
        self.set_applied_voltage_btn.clicked.connect(self.set_applied_voltage)
        calibration_layout.addWidget(self.set_applied_voltage_btn, 0, 2)
        
        self.cal_voltage_btn = QPushButton("Calibrate Voltage")
        self.cal_voltage_btn.setMinimumHeight(35)
        self.cal_voltage_btn.clicked.connect(lambda: self.send_command("fg_cal_voltage"))
        self.cal_voltage_btn.setStyleSheet("background-color: #fdcb6e;")
        calibration_layout.addWidget(self.cal_voltage_btn, 0, 3)
        
        # Current calibration
        calibration_layout.addWidget(QLabel("Applied Current (A):"), 1, 0)
        self.applied_current_input = QDoubleSpinBox()
        self.applied_current_input.setRange(-10.0, -1.0)
        self.applied_current_input.setValue(-4.0)
        self.applied_current_input.setSingleStep(0.1)
        self.applied_current_input.setDecimals(1)
        self.applied_current_input.setMinimumHeight(30)
        calibration_layout.addWidget(self.applied_current_input, 1, 1)
        
        self.set_applied_current_btn = QPushButton("Set Applied Current")
        self.set_applied_current_btn.setMinimumHeight(35)
        self.set_applied_current_btn.clicked.connect(self.set_applied_current)
        calibration_layout.addWidget(self.set_applied_current_btn, 1, 2)
        
        self.cal_current_btn = QPushButton("Calibrate Current")
        self.cal_current_btn.setMinimumHeight(35)
        self.cal_current_btn.clicked.connect(lambda: self.send_command("fg_cal_current"))
        self.cal_current_btn.setStyleSheet("background-color: #fdcb6e;")
        calibration_layout.addWidget(self.cal_current_btn, 1, 3)
        
        # Other calibrations
        self.cal_cc_offset_btn = QPushButton("Calibrate CC Offset")
        self.cal_cc_offset_btn.setMinimumHeight(35)
        self.cal_cc_offset_btn.clicked.connect(lambda: self.send_command("fg_cal_cc_offset"))
        self.cal_cc_offset_btn.setStyleSheet("background-color: #fdcb6e;")
        calibration_layout.addWidget(self.cal_cc_offset_btn, 2, 0)
        
        self.cal_board_offset_btn = QPushButton("Calibrate Board Offset")
        self.cal_board_offset_btn.setMinimumHeight(35)
        self.cal_board_offset_btn.clicked.connect(lambda: self.send_command("fg_cal_board_offset"))
        self.cal_board_offset_btn.setStyleSheet("background-color: #fdcb6e;")
        calibration_layout.addWidget(self.cal_board_offset_btn, 2, 1)
        
        calibration_group.setLayout(calibration_layout)
        scroll_layout.addWidget(calibration_group)
        
        # Serial Number Group
        serial_group = QGroupBox("Serial Number")
        serial_layout = QGridLayout()
        serial_layout.setSpacing(10)
        
        serial_layout.addWidget(QLabel("Serial Number:"), 0, 0)
        self.serial_number_input = QLineEdit()
        self.serial_number_input.setPlaceholderText("Enter serial number")
        self.serial_number_input.setMinimumHeight(30)
        serial_layout.addWidget(self.serial_number_input, 0, 1)
        
        self.set_serial_btn = QPushButton("Set Serial Number")
        self.set_serial_btn.setMinimumHeight(35)
        self.set_serial_btn.clicked.connect(self.set_serial_number)
        serial_layout.addWidget(self.set_serial_btn, 0, 2)
        
        self.store_serial_btn = QPushButton("Store Serial Number")
        self.store_serial_btn.setMinimumHeight(35)
        self.store_serial_btn.clicked.connect(lambda: self.send_command("serial_number_store"))
        serial_layout.addWidget(self.store_serial_btn, 0, 3)
        
        serial_group.setLayout(serial_layout)
        scroll_layout.addWidget(serial_group)
        
        # Add some spacing at the bottom
        scroll_layout.addStretch()
        
        # Set up scroll area
        scroll.setWidget(scroll_widget)
        left_widget.setLayout(QVBoxLayout())
        left_widget.layout().addWidget(scroll)
        
        # Right side - Command Response Log
        right_widget = QWidget()
        right_widget.setMinimumWidth(400)  # Ensure adequate width for log
        right_layout = QVBoxLayout()
        
        response_group = QGroupBox("Command Response Log")
        response_layout = QVBoxLayout()
        
        self.response_log = QTextEdit()
        self.response_log.setReadOnly(True)
        self.response_log.setFont(QFont("Courier", 9))
        response_layout.addWidget(self.response_log)
        
        # Clear log button
        clear_log_btn = QPushButton("Clear Log")
        clear_log_btn.setMaximumHeight(35)
        clear_log_btn.clicked.connect(self.clear_response_log)
        response_layout.addWidget(clear_log_btn)
        
        response_group.setLayout(response_layout)
        right_layout.addWidget(response_group)
        right_widget.setLayout(right_layout)
        
        # Add widgets to splitter
        main_splitter.addWidget(left_widget)
        main_splitter.addWidget(right_widget)
        
        # Set splitter proportions (roughly 60/40)
        main_splitter.setSizes([650, 400])
        
        layout.addWidget(main_splitter)
        self.setLayout(layout)
        
        # Connect to command response signal
        self.commandResponse.connect(self.handle_command_response)
    
    def send_command(self, command_name, *args):
        """Send a Modbus command"""
        if not self.modbus_worker:
            self.log_response(command_name, "ERROR: Modbus worker not initialized")
            return
        
        if not hasattr(self.modbus_worker, 'client') or not self.modbus_worker.client:
            self.log_response(command_name, "ERROR: Not connected to Modbus device. Start monitoring first.")
            return
        
        if not self.modbus_worker.client.connected:
            self.log_response(command_name, "ERROR: Modbus connection lost. Restart monitoring.")
            return
        
        try:
            # Map command names to Modbus coil/register addresses based on the C# code
            command_map = {
                "charge_fet_on": (10, True),
                "charge_fet_off": (10, False),
                "discharge_fet_on": (11, True),
                "discharge_fet_off": (11, False),
                "reset_micro": (12, True),
                "fg_reset": (13, True),
                "fg_seal": (14, True),
                "fg_unseal": (14, False),
                "fg_full_unseal": (15, False),
                "rebalance_mode": (16, args[0] if args else False),
                "fg_lt_enable": (17, True),
                "fg_program": (18, True),
                "fg_cal_cc_offset": (19, True),
                "fg_cal_board_offset": (20, True),
                "fg_cal_voltage": (21, True),
                "fg_cal_current": (22, True),
                "serial_number_store": (23, True),
                "fg_full_unseal": (15, False)
            }
            
            if command_name in command_map:
                address, value = command_map[command_name]
                if command_name.startswith("rebalance_mode"):
                    value = bool(args[0]) if args else False
                
                # Write single coil
                response = self.modbus_worker.client.write_coil(
                    address=address,
                    value=value,
                    slave=self.modbus_worker.slave_id
                )
                
                if response.isError():
                    self.log_response(command_name, f"ERROR: {response}")
                else:
                    self.log_response(command_name, f"SUCCESS: Coil {address} set to {value}")
            else:
                self.log_response(command_name, "ERROR: Unknown command")
                
        except Exception as e:
            self.log_response(command_name, f"ERROR: {str(e)}")
    
    def _check_modbus_connection(self, command_name):
        """Check if Modbus connection is available"""
        if not self.modbus_worker:
            self.log_response(command_name, "ERROR: Modbus worker not initialized")
            return False
        
        if not hasattr(self.modbus_worker, 'client') or not self.modbus_worker.client:
            self.log_response(command_name, "ERROR: Not connected to Modbus device. Start monitoring first.")
            return False
        
        if not self.modbus_worker.client.connected:
            self.log_response(command_name, "ERROR: Modbus connection lost. Restart monitoring.")
            return False
        
        return True
    
    def set_unseal_key(self):
        """Set the unseal key"""
        if not self._check_modbus_connection("set_unseal_key"):
            return
            
        try:
            key_str = self.unseal_key_input.text().strip()
            key_value = int(key_str, 16)  # Convert hex string to int
            
            # Split into high and low 16-bit words
            key_hi = (key_value >> 16) & 0xFFFF
            key_lo = key_value & 0xFFFF
            
            # Write to holding registers 1002-1003
            response = self.modbus_worker.client.write_registers(
                address=1002,
                values=[key_hi, key_lo],
                slave=self.modbus_worker.slave_id
            )
            
            if response.isError():
                self.log_response("set_unseal_key", f"ERROR: {response}")
            else:
                self.log_response("set_unseal_key", f"SUCCESS: Unseal key 0x{key_str} set")
                
        except ValueError:
            self.log_response("set_unseal_key", "ERROR: Invalid hex format")
        except Exception as e:
            self.log_response("set_unseal_key", f"ERROR: {str(e)}")
    
    def set_full_unseal_key(self):
        """Set the full unseal key"""
        if not self._check_modbus_connection("set_full_unseal_key"):
            return
            
        try:
            key_str = self.full_unseal_key_input.text().strip()
            key_value = int(key_str, 16)  # Convert hex string to int
            
            # Split into high and low 16-bit words
            key_hi = (key_value >> 16) & 0xFFFF
            key_lo = key_value & 0xFFFF
            
            # Write to holding registers 1004-1005
            response = self.modbus_worker.client.write_registers(
                address=1004,
                values=[key_hi, key_lo],
                slave=self.modbus_worker.slave_id
            )
            
            if response.isError():
                self.log_response("set_full_unseal_key", f"ERROR: {response}")
            else:
                self.log_response("set_full_unseal_key", f"SUCCESS: Full unseal key 0x{key_str} set")
                
        except ValueError:
            self.log_response("set_full_unseal_key", "ERROR: Invalid hex format")
        except Exception as e:
            self.log_response("set_full_unseal_key", f"ERROR: {str(e)}")
    
    def set_applied_voltage(self):
        """Set applied voltage for calibration"""
        if not self._check_modbus_connection("set_applied_voltage"):
            return
            
        try:
            voltage = self.applied_voltage_input.value()
            voltage_mv = int(voltage * 1000)  # Convert to mV
            
            # Write to holding register 1006
            response = self.modbus_worker.client.write_register(
                address=1006,
                value=voltage_mv,
                slave=self.modbus_worker.slave_id
            )
            
            if response.isError():
                self.log_response("set_applied_voltage", f"ERROR: {response}")
            else:
                self.log_response("set_applied_voltage", f"SUCCESS: Applied voltage set to {voltage}V")
                
        except Exception as e:
            self.log_response("set_applied_voltage", f"ERROR: {str(e)}")
    
    def set_applied_current(self):
        """Set applied current for calibration"""
        if not self._check_modbus_connection("set_applied_current"):
            return
            
        try:
            current = self.applied_current_input.value()
            current_ma = int(current * 1000)  # Convert to mA
            
            # Handle signed 16-bit value
            if current_ma < 0:
                current_ma = 65536 + current_ma  # Convert to unsigned 16-bit
            
            # Try write_registers (plural) instead of write_register (singular)
            self.log_response("set_applied_current", f"DEBUG: Writing [{current_ma}] to register 1007 using write_registers")
            response = self.modbus_worker.client.write_registers(
                address=1007,
                values=[current_ma],
                slave=self.modbus_worker.slave_id
            )
            
            if response.isError():
                self.log_response("set_applied_current", f"ERROR: {response}")
            else:
                self.log_response("set_applied_current", f"SUCCESS: Applied current set to {current}A")
                
        except Exception as e:
            self.log_response("set_applied_current", f"ERROR: {str(e)}")
    
    def set_serial_number(self):
        """Set serial number"""
        if not self._check_modbus_connection("set_serial_number"):
            return
            
        try:
            serial_str = self.serial_number_input.text().strip()
            if not serial_str:
                self.log_response("set_serial_number", "ERROR: Serial number cannot be empty")
                return
            
            serial_num = int(serial_str)
            
            # Split into high and low 16-bit words
            serial_hi = (serial_num >> 16) & 0xFFFF
            serial_lo = serial_num & 0xFFFF
            
            # Write to holding registers 1000-1001
            response = self.modbus_worker.client.write_registers(
                address=1000,
                values=[serial_hi, serial_lo],
                slave=self.modbus_worker.slave_id
            )
            
            if response.isError():
                self.log_response("set_serial_number", f"ERROR: {response}")
            else:
                self.log_response("set_serial_number", f"SUCCESS: Serial number {serial_str} set")
                
        except ValueError:
            self.log_response("set_serial_number", "ERROR: Invalid serial number format")
        except Exception as e:
            self.log_response("set_serial_number", f"ERROR: {str(e)}")
    
    def check_unseal_status(self):
        """Read coil 15 to check if fuel gauge is fully unsealed"""
        if not self._check_modbus_connection("check_unseal_status"):
            return
            
        try:
            # Read coil 15 - returns True if sealed, False if fully unsealed
            response = self.modbus_worker.client.read_coils(
                address=15,
                count=1,
                slave=self.modbus_worker.slave_id
            )
            
            if response.isError():
                self.log_response("check_unseal_status", f"ERROR: {response}")
            else:
                is_sealed = response.bits[0]
                status = "SEALED" if is_sealed else "FULLY UNSEALED"
                self.log_response("check_unseal_status", f"Coil 15 = {is_sealed} ({status})")
                
        except Exception as e:
            self.log_response("check_unseal_status", f"ERROR: {str(e)}")
    
    def test_holding_registers(self):
        """Test reading holding registers to see which ones exist"""
        if not self._check_modbus_connection("test_holding_registers"):
            return
            
        try:
            # Try reading holding registers 1000-1007 (firmware range)
            for addr in range(1000, 1008):
                try:
                    response = self.modbus_worker.client.read_holding_registers(
                        address=addr,
                        count=1,
                        slave=self.modbus_worker.slave_id
                    )
                    if not response.isError():
                        self.log_response("test_holding_registers", f"Register {addr}: {response.registers[0]}")
                    else:
                        self.log_response("test_holding_registers", f"Register {addr}: ERROR")
                except Exception as e:
                    self.log_response("test_holding_registers", f"Register {addr}: {str(e)}")
                    
        except Exception as e:
            self.log_response("test_holding_registers", f"ERROR: {str(e)}")
    
    def log_response(self, command, response):
        """Log command response"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f"[{timestamp}] {command}: {response}"
        self.response_log.append(log_entry)
        self.commandResponse.emit(command, response)
    
    def handle_command_response(self, command_name, response):
        """Handle command response signal"""
        # This can be used for additional processing if needed
        pass
    
    def clear_response_log(self):
        """Clear the response log"""
        self.response_log.clear()
    
    def update_connection_status(self, connected=False):
        """Update the connection status indicator"""
        if connected:
            self.connection_status_label.setText("🟢 Connected - Commands available")
            self.connection_status_label.setStyleSheet("color: #00b894; background-color: #d1f2eb; padding: 8px; border: 1px solid #00b894; border-radius: 5px; font-weight: bold;")
        else:
            self.connection_status_label.setText("🔴 Not Connected - Start monitoring to enable commands")
            self.connection_status_label.setStyleSheet("color: #d63031; background-color: #fff5f5; padding: 8px; border: 1px solid #fab1a0; border-radius: 5px; font-weight: bold;")


class ChangelogViewerWidget(QWidget):
    """Widget for viewing CHANGELOG.md with version tracking"""
    
    # Application version
    APP_VERSION = "1.2.1"
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_changelog()
    
    def setup_ui(self):
        """Setup the changelog viewer UI"""
        layout = QVBoxLayout()
        
        # Header with version info
        header_layout = QHBoxLayout()
        
        version_label = QLabel(f"<h2>📋 Changelog - Current Version: {self.APP_VERSION}</h2>")
        header_layout.addWidget(version_label)
        
        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self.load_changelog)
        refresh_btn.setMaximumWidth(100)
        header_layout.addWidget(refresh_btn)
        
        layout.addLayout(header_layout)
        
        # Changelog content viewer
        self.changelog_text = QTextEdit()
        self.changelog_text.setReadOnly(True)
        self.changelog_text.setFont(QFont("Consolas", 10))  # Monospace font for better markdown
        layout.addWidget(self.changelog_text)
        
        # Footer with file info
        self.footer_label = QLabel("CHANGELOG.md")
        self.footer_label.setStyleSheet("color: gray; font-size: 10px;")
        layout.addWidget(self.footer_label)
        
        self.setLayout(layout)
    
    def load_changelog(self):
        """Load and display the CHANGELOG.md file"""
        changelog_path = "CHANGELOG.md"
        
        try:
            if os.path.exists(changelog_path):
                with open(changelog_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Convert basic markdown to HTML for better display
                html_content = self.markdown_to_html(content)
                self.changelog_text.setHtml(html_content)
                
                # Update footer with file info
                file_stat = os.stat(changelog_path)
                modified_time = datetime.fromtimestamp(file_stat.st_mtime)
                self.footer_label.setText(
                    f"CHANGELOG.md - Last modified: {modified_time.strftime('%Y-%m-%d %H:%M:%S')}"
                )
            else:
                self.changelog_text.setPlainText("CHANGELOG.md not found in the project directory.")
                self.footer_label.setText("CHANGELOG.md - File not found")
                
        except Exception as e:
            self.changelog_text.setPlainText(f"Error loading CHANGELOG.md: {e}")
            self.footer_label.setText("CHANGELOG.md - Error loading file")
    
    def markdown_to_html(self, markdown_text):
        """Convert basic markdown to HTML for display"""
        html = markdown_text
        
        # Headers
        html = html.replace('### ', '<h3>')
        html = html.replace('## ', '<h2>')
        html = html.replace('# ', '<h1>')
        
        # Close header tags at end of line
        lines = html.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('<h1>'):
                lines[i] = line + '</h1>'
            elif line.startswith('<h2>'):
                lines[i] = line + '</h2>'
            elif line.startswith('<h3>'):
                lines[i] = line + '</h3>'
        
        html = '\n'.join(lines)
        
        # Bold text
        html = html.replace('**', '<b>', 1).replace('**', '</b>', 1)
        while '**' in html:
            html = html.replace('**', '<b>', 1).replace('**', '</b>', 1)
        
        # Lists
        html = html.replace('- ', '• ')
        
        # Code blocks (basic)
        html = html.replace('`', '<code>').replace('`', '</code>')
        
        # Line breaks for better formatting
        html = html.replace('\n', '<br>')
        
        # Links
        import re
        link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
        html = re.sub(link_pattern, r'<a href="\2">\1</a>', html)
        
        # Add basic styling
        styled_html = f"""
        <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }}
        h1 {{ color: #2c3e50; border-bottom: 2px solid #3498db; }}
        h2 {{ color: #34495e; border-bottom: 1px solid #bdc3c7; }}
        h3 {{ color: #7f8c8d; }}
        code {{ background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; }}
        a {{ color: #3498db; }}
        </style>
        <body>{html}</body>
        """
        
        return styled_html
    
    def get_current_version(self):
        """Get the current application version"""
        return self.APP_VERSION


class SettingsWidget(QWidget):
    """Widget for configuring application settings and thresholds"""
    
    # Theme signal
    themeChanged = pyqtSignal(str)
    
    def __init__(self, settings: QSettings):
        super().__init__()
        self.settings = settings
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """Setup the settings widget UI"""
        layout = QVBoxLayout()
        
        # Theme Settings Group
        theme_group = QGroupBox("Application Theme")
        theme_layout = QFormLayout()
        
        # Theme selection
        self.theme_combo = QComboBox()
        self.theme_combo.addItem("Light Theme", "light")
        self.theme_combo.addItem("Dark Theme", "dark")
        self.theme_combo.addItem("System Default", "system")
        self.theme_combo.currentTextChanged.connect(self.on_theme_changed)
        theme_layout.addRow("Theme:", self.theme_combo)
        
        theme_group.setLayout(theme_layout)
        layout.addWidget(theme_group)
        
        # Voltage Monitoring Settings Group
        voltage_group = QGroupBox("Voltage Monitoring Thresholds")
        voltage_layout = QFormLayout()
        
        # Cell Delta Warning Threshold
        self.cell_delta_threshold = QDoubleSpinBox()
        self.cell_delta_threshold.setRange(1.0, 1000.0)  # 1mV to 1000mV
        self.cell_delta_threshold.setValue(30.0)  # Default 30mV
        self.cell_delta_threshold.setSuffix(" mV")
        self.cell_delta_threshold.setDecimals(1)
        voltage_layout.addRow("Cell Delta Warning Threshold:", self.cell_delta_threshold)
        
        # Pack Voltage Min Threshold
        self.pack_volt_min = QDoubleSpinBox()
        self.pack_volt_min.setRange(0.0, 50.0)  # 0V to 50V
        self.pack_volt_min.setValue(22.0)  # Default 22V
        self.pack_volt_min.setSuffix(" V")
        self.pack_volt_min.setDecimals(1)
        voltage_layout.addRow("Pack Voltage Min Threshold:", self.pack_volt_min)
        
        # Pack Voltage Max Threshold
        self.pack_volt_max = QDoubleSpinBox()
        self.pack_volt_max.setRange(0.0, 50.0)  # 0V to 50V
        self.pack_volt_max.setValue(28.6)  # Default 28.6V
        self.pack_volt_max.setSuffix(" V")
        self.pack_volt_max.setDecimals(1)
        voltage_layout.addRow("Pack Voltage Max Threshold:", self.pack_volt_max)
        
        voltage_group.setLayout(voltage_layout)
        layout.addWidget(voltage_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        save_btn = QPushButton("Save Settings")
        save_btn.clicked.connect(self.save_settings)
        button_layout.addWidget(save_btn)
        
        reset_btn = QPushButton("Reset to Defaults")
        reset_btn.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(reset_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # Add spacer
        layout.addStretch()
        
        self.setLayout(layout)
    
    def load_settings(self):
        """Load settings from QSettings"""
        # Load theme setting
        theme = self.settings.value("theme", "light", type=str)
        index = self.theme_combo.findData(theme)
        if index >= 0:
            self.theme_combo.setCurrentIndex(index)
        
        # Load threshold values
        cell_delta = self.settings.value("cell_delta_threshold", 30.0, type=float)
        self.cell_delta_threshold.setValue(cell_delta)
        
        pack_min = self.settings.value("pack_volt_min", 22.0, type=float)
        self.pack_volt_min.setValue(pack_min)
        
        pack_max = self.settings.value("pack_volt_max", 28.6, type=float)
        self.pack_volt_max.setValue(pack_max)
    
    def save_settings(self):
        """Save settings to QSettings"""
        self.settings.setValue("theme", self.theme_combo.currentData())
        self.settings.setValue("cell_delta_threshold", self.cell_delta_threshold.value())
        self.settings.setValue("pack_volt_min", self.pack_volt_min.value())
        self.settings.setValue("pack_volt_max", self.pack_volt_max.value())
        
        QMessageBox.information(self, "Settings", "Settings saved successfully!")
    
    def reset_to_defaults(self):
        """Reset all settings to default values"""
        self.theme_combo.setCurrentIndex(0)  # Light theme
        self.cell_delta_threshold.setValue(30.0)
        self.pack_volt_min.setValue(22.0)
        self.pack_volt_max.setValue(28.6)
        
        QMessageBox.information(self, "Settings", "Settings reset to defaults!")
    
    def get_cell_delta_threshold(self) -> float:
        """Get current cell delta threshold in mV"""
        return self.cell_delta_threshold.value()
    
    def get_pack_volt_min(self) -> float:
        """Get current pack voltage minimum threshold in V"""
        return self.pack_volt_min.value()
    
    def get_pack_volt_max(self) -> float:
        """Get current pack voltage maximum threshold in V"""
        return self.pack_volt_max.value()
    
    def on_theme_changed(self):
        """Handle theme change"""
        current_theme = self.theme_combo.currentData()
        self.themeChanged.emit(current_theme)
    
    def get_current_theme(self) -> str:
        """Get current selected theme"""
        return self.theme_combo.currentData()


class BMSMainWindow(QMainWindow):
    """Main application window"""
    
    # Application version - synchronized with ChangelogViewerWidget
    APP_VERSION = "1.2.1"
    
    def __init__(self):
        super().__init__()
        self.worker = None
        self.csv_writer = None
        self.csv_filename = None
        self.logging_enabled = False

        # Initialize settings for remembering user preferences
        self.settings = QSettings("GA_BMS", "BatteryMonitor")
        
        # Initialize session manager if available
        if SessionManager:
            self.session_manager = SessionManager(self.settings)
            self.session_manager.session_started.connect(self.on_session_started)
            self.session_manager.session_ended.connect(self.on_session_ended)
            self.session_manager.auto_start_triggered.connect(self.on_auto_start_triggered)
        else:
            self.session_manager = None

        self.setup_ui()
        self.setup_connections()
        self.load_settings()
    
    def setup_ui(self):
        """Setup the main window UI"""
        self.setWindowTitle(f"GA Battery Management System - Real-time Monitor v{self.APP_VERSION}")
        self.setGeometry(100, 100, 1400, 900)
        
        # Central widget with splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # Control panel
        control_group = self.create_control_panel()
        main_layout.addWidget(control_group)
        
        # Create tab widget for different views
        self.tab_widget = QTabWidget()
        
        # Tab 1: Real-time View (plots and current data)
        realtime_widget = QWidget()
        realtime_layout = QVBoxLayout()
        realtime_widget.setLayout(realtime_layout)
        
        # Add session status indicator at top if session manager available
        if self.session_manager:
            self.realtime_session_label = QLabel("Session: Not active")
            self.realtime_session_label.setStyleSheet("background-color: #f0f0f0; padding: 5px; border: 1px solid #ccc; font-weight: bold;")
            realtime_layout.addWidget(self.realtime_session_label)
        
        # Create splitter for plots and table
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left side - plots
        plots_widget = QWidget()
        plots_layout = QVBoxLayout()
        plots_widget.setLayout(plots_layout)
        
        # Cell voltage plot
        self.cell_voltage_plot = RealTimePlotWidget("Cell Voltages", "Voltage (V)")
        plots_layout.addWidget(self.cell_voltage_plot)
        
        # Current and pack voltage plot
        self.pack_plot = RealTimePlotWidget("Pack Voltage & Current", "Value")
        plots_layout.addWidget(self.pack_plot)
        
        # Right side - data table
        self.data_table = DataTableWidget()
        
        splitter.addWidget(plots_widget)
        splitter.addWidget(self.data_table)
        splitter.setSizes([800, 400])
        
        realtime_layout.addWidget(splitter)
        
        # Tab 2: CSV Log View
        csv_log_widget = QWidget()
        csv_log_layout = QVBoxLayout()
        csv_log_widget.setLayout(csv_log_layout)
        
        # CSV log controls
        csv_controls_layout = QHBoxLayout()
        clear_log_btn = QPushButton("Clear Log")
        clear_log_btn.clicked.connect(self.clear_csv_log)
        csv_controls_layout.addWidget(clear_log_btn)
        csv_controls_layout.addStretch()
        
        # CSV log table
        self.csv_log_table = CSVLogTableWidget(max_rows=200)
        
        csv_log_layout.addLayout(csv_controls_layout)
        csv_log_layout.addWidget(self.csv_log_table)
        
        # Tab 3: Commands - will be initialized in setup_connections()
        self.commands_widget = None
        
        # Tab 4: CSV Viewer
        self.csv_viewer_widget = CSVViewerWidget()
        
        # Tab 5: Changelog
        self.changelog_widget = ChangelogViewerWidget()
        
        # Tab 6: Session Management
        if self.session_manager:
            self.session_widget = self.create_session_widget()
        else:
            self.session_widget = QLabel("Session Management not available")
        
        # Tab 7: Settings
        self.settings_widget = SettingsWidget(self.settings)
        
        # Connect settings to data table for threshold access
        self.data_table.set_settings_widget(self.settings_widget)
        
        # Connect theme changes
        self.settings_widget.themeChanged.connect(self.apply_theme)
        
        # Apply initial theme
        initial_theme = self.settings_widget.get_current_theme()
        self.apply_theme(initial_theme)
        
        # Add tabs
        self.tab_widget.addTab(self.session_widget, "Sessions")
        self.tab_widget.addTab(realtime_widget, "Real-time View")
        self.tab_widget.addTab(csv_log_widget, "CSV Log")
        # Commands tab will be added in setup_connections() after worker is initialized
        self.tab_widget.addTab(self.csv_viewer_widget, "CSV Viewer")
        self.tab_widget.addTab(self.changelog_widget, "Changelog")
        self.tab_widget.addTab(self.settings_widget, "Settings")
        
        main_layout.addWidget(self.tab_widget)
        
        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
    
    def create_control_panel(self) -> QGroupBox:
        """Create the control panel"""
        group = QGroupBox("Connection & Control")
        layout = QGridLayout()
        
        # Serial port selection
        layout.addWidget(QLabel("Serial Port:"), 0, 0)
        self.port_combo = QComboBox()
        self.refresh_ports()
        layout.addWidget(self.port_combo, 0, 1)
        
        refresh_btn = QPushButton("Refresh")
        refresh_btn.clicked.connect(self.refresh_ports)
        layout.addWidget(refresh_btn, 0, 2)
        
        # Baud rate
        layout.addWidget(QLabel("Baud Rate:"), 0, 3)
        self.baudrate_spin = QSpinBox()
        self.baudrate_spin.setRange(1200, 115200)
        self.baudrate_spin.setValue(9600)
        layout.addWidget(self.baudrate_spin, 0, 4)
        
        # Slave ID
        layout.addWidget(QLabel("Slave ID:"), 0, 5)
        self.slave_id_spin = QSpinBox()
        self.slave_id_spin.setRange(1, 247)
        self.slave_id_spin.setValue(1)
        layout.addWidget(self.slave_id_spin, 0, 6)
        
        # Control buttons
        self.start_btn = QPushButton("Start Monitoring")
        self.start_btn.clicked.connect(self.start_monitoring)
        layout.addWidget(self.start_btn, 1, 0, 1, 2)
        
        self.stop_btn = QPushButton("Stop Monitoring")
        self.stop_btn.clicked.connect(self.stop_monitoring)
        self.stop_btn.setEnabled(False)
        layout.addWidget(self.stop_btn, 1, 2, 1, 2)
        
        # CSV logging checkbox
        self.logging_checkbox = QCheckBox("Enable CSV Logging")
        layout.addWidget(self.logging_checkbox, 1, 4, 1, 2)
        
        # Choose CSV file button
        self.csv_btn = QPushButton("Choose CSV File")
        self.csv_btn.clicked.connect(self.choose_csv_file)
        layout.addWidget(self.csv_btn, 1, 6)
        
        group.setLayout(layout)
        return group
    
    def create_session_widget(self) -> QWidget:
        """Create the session management widget"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # Current session status
        status_group = QGroupBox("Current Session")
        status_layout = QVBoxLayout()
        status_group.setLayout(status_layout)
        
        self.session_status_label = QLabel("No active session")
        self.session_status_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        status_layout.addWidget(self.session_status_label)
        
        # Session info labels
        self.session_info_layout = QVBoxLayout()
        status_layout.addLayout(self.session_info_layout)
        
        # Auto-start configuration
        auto_group = QGroupBox("Auto-Start Configuration")
        auto_layout = QGridLayout()
        auto_group.setLayout(auto_layout)
        
        # Auto-start mode
        auto_layout.addWidget(QLabel("Auto-Start Mode:"), 0, 0)
        self.auto_mode_combo = QComboBox()
        self.auto_mode_combo.addItems(['OFF', 'ENABLE'])
        self.auto_mode_combo.currentTextChanged.connect(self.update_auto_config)
        auto_layout.addWidget(self.auto_mode_combo, 0, 1)
        
        # Charging threshold
        auto_layout.addWidget(QLabel("Charging Threshold (A):"), 1, 0)
        self.charge_threshold_spin = QDoubleSpinBox()
        self.charge_threshold_spin.setRange(-100.0, 100.0)
        self.charge_threshold_spin.setValue(0.5)
        self.charge_threshold_spin.setSingleStep(0.1)
        self.charge_threshold_spin.valueChanged.connect(self.update_auto_config)
        auto_layout.addWidget(self.charge_threshold_spin, 1, 1)
        
        # Discharging threshold
        auto_layout.addWidget(QLabel("Discharging Threshold (A):"), 2, 0)
        self.discharge_threshold_spin = QDoubleSpinBox()
        self.discharge_threshold_spin.setRange(-100.0, 100.0)
        self.discharge_threshold_spin.setValue(-0.5)
        self.discharge_threshold_spin.setSingleStep(0.1)
        self.discharge_threshold_spin.valueChanged.connect(self.update_auto_config)
        auto_layout.addWidget(self.discharge_threshold_spin, 2, 1)
        
        # Enable checkboxes
        self.charge_enable_check = QCheckBox("Enable Auto-Start on Charging")
        self.charge_enable_check.setChecked(True)
        self.charge_enable_check.toggled.connect(self.update_auto_config)
        auto_layout.addWidget(self.charge_enable_check, 3, 0, 1, 2)
        
        self.discharge_enable_check = QCheckBox("Enable Auto-Start on Discharging")
        self.discharge_enable_check.setChecked(True)
        self.discharge_enable_check.toggled.connect(self.update_auto_config)
        auto_layout.addWidget(self.discharge_enable_check, 4, 0, 1, 2)
        
        # Session history
        history_group = QGroupBox("Session History")
        history_layout = QVBoxLayout()
        history_group.setLayout(history_layout)
        
        # Refresh button
        refresh_btn = QPushButton("Refresh History")
        refresh_btn.clicked.connect(self.refresh_session_history)
        history_layout.addWidget(refresh_btn)
        
        # Session table
        self.session_table = QTableWidget()
        self.session_table.setColumnCount(6)
        self.session_table.setHorizontalHeaderLabels([
            "Session ID", "Start Time", "Duration", "Trigger", "Records", "Files"
        ])
        self.session_table.horizontalHeader().setStretchLastSection(True)
        history_layout.addWidget(self.session_table)
        
        # Add groups to main layout
        layout.addWidget(status_group)
        layout.addWidget(auto_group)
        layout.addWidget(history_group, 1)  # Stretch for history
        
        # Initialize with current config
        self.load_session_config()
        self.refresh_session_history()
        
        # Set up timer to update current session status
        self.session_timer = QTimer()
        self.session_timer.timeout.connect(self.update_session_status)
        self.session_timer.start(1000)  # Update every second
        
        return widget
    
    def load_session_config(self):
        """Load session configuration into UI"""
        if not self.session_manager:
            return
        
        config = self.session_manager.auto_config
        self.auto_mode_combo.setCurrentText(config.auto_log_mode)
        self.charge_threshold_spin.setValue(config.auto_log_charging_threshold)
        self.discharge_threshold_spin.setValue(config.auto_log_discharging_threshold)
        self.charge_enable_check.setChecked(config.auto_log_charge_enable)
        self.discharge_enable_check.setChecked(config.auto_log_discharge_enable)
    
    def update_auto_config(self):
        """Update auto-start configuration"""
        if not self.session_manager:
            return
        
        config = self.session_manager.auto_config
        config.auto_log_mode = self.auto_mode_combo.currentText()
        config.auto_log_charging_threshold = self.charge_threshold_spin.value()
        config.auto_log_discharging_threshold = self.discharge_threshold_spin.value()
        config.auto_log_charge_enable = self.charge_enable_check.isChecked()
        config.auto_log_discharge_enable = self.discharge_enable_check.isChecked()
        
        self.session_manager.save_auto_config()
    
    def refresh_session_history(self):
        """Refresh the session history table"""
        if not self.session_manager:
            return
        
        sessions = self.session_manager.get_session_history()
        self.session_table.setRowCount(len(sessions))
        
        for i, session in enumerate(sessions):
            # Session ID (short)
            session_id_item = QTableWidgetItem(session.session_id[:8] + "...")
            session_id_item.setData(Qt.ItemDataRole.UserRole, session.session_id)
            self.session_table.setItem(i, 0, session_id_item)
            
            # Start time
            start_time = session.start_time.strftime('%Y-%m-%d %H:%M:%S') if session.start_time else "N/A"
            self.session_table.setItem(i, 1, QTableWidgetItem(start_time))
            
            # Duration
            duration_str = f"{session.duration_seconds}s" if session.duration_seconds else "N/A"
            self.session_table.setItem(i, 2, QTableWidgetItem(duration_str))
            
            # Trigger type
            self.session_table.setItem(i, 3, QTableWidgetItem(session.trigger_type))
            
            # Record count
            record_count = session.data_summary.get('total_records', 0)
            self.session_table.setItem(i, 4, QTableWidgetItem(str(record_count)))
            
            # CSV files
            file_count = len(session.csv_files)
            self.session_table.setItem(i, 5, QTableWidgetItem(str(file_count)))
    
    def update_session_status(self):
        """Update current session status display"""
        if not self.session_manager:
            return
        
        status = self.session_manager.get_current_session_status()
        
        if status['active']:
            # Clear previous info
            for i in reversed(range(self.session_info_layout.count())):
                child = self.session_info_layout.takeAt(i).widget()
                if child:
                    child.deleteLater()
            
            # Update status
            session_id = status['session_id'][:8] + "..." if status['session_id'] else "N/A"
            trigger_type = status['trigger_type'] or "manual"
            duration = status['duration']
            record_count = status['record_count']
            
            self.session_status_label.setText(f"Active Session: {session_id}")
            
            # Update real-time view indicator
            if hasattr(self, 'realtime_session_label'):
                self.realtime_session_label.setText(f"Session: {session_id} ({trigger_type}) - {duration // 60}m {duration % 60}s")
                self.realtime_session_label.setStyleSheet("background-color: #d4edda; padding: 5px; border: 1px solid #c3e6cb; font-weight: bold; color: #155724;")
            
            info_labels = [
                f"Trigger: {trigger_type}",
                f"Duration: {duration // 60}m {duration % 60}s",
                f"Records: {record_count}",
                f"CSV Files: {status['csv_files']}"
            ]
            
            for text in info_labels:
                label = QLabel(text)
                self.session_info_layout.addWidget(label)
        else:
            self.session_status_label.setText("No active session")
            
            # Update real-time view indicator
            if hasattr(self, 'realtime_session_label'):
                self.realtime_session_label.setText("Session: Not active")
                self.realtime_session_label.setStyleSheet("background-color: #f0f0f0; padding: 5px; border: 1px solid #ccc; font-weight: bold;")
            
            # Clear info
            for i in reversed(range(self.session_info_layout.count())):
                child = self.session_info_layout.takeAt(i).widget()
                if child:
                    child.deleteLater()
    
    def setup_connections(self):
        """Setup signal connections"""
        # Initialize Modbus worker
        self.worker = ModbusWorker(self.session_manager)
        self.worker.dataReceived.connect(self.handle_new_data)
        self.worker.statusChanged.connect(self.handle_status_change)
        self.worker.errorOccurred.connect(self.handle_error)
        
        # Now initialize Commands widget with the worker
        self.commands_widget = CommandsWidget(self.worker)
        
        # Add Commands tab to the tab widget
        self.tab_widget.addTab(self.commands_widget, "Commands")

    def load_settings(self):
        """Load user settings from QSettings"""
        # Load last used serial port
        last_port = self.settings.value("serial_port", "")
        if last_port:
            index = self.port_combo.findData(last_port)
            if index >= 0:
                self.port_combo.setCurrentIndex(index)

        # Load other settings
        baudrate = self.settings.value("baudrate", 9600, type=int)
        self.baudrate_spin.setValue(baudrate)

        slave_id = self.settings.value("slave_id", 1, type=int)
        self.slave_id_spin.setValue(slave_id)

        # Load CSV logging preference
        logging_enabled = self.settings.value("csv_logging", False, type=bool)
        self.logging_checkbox.setChecked(logging_enabled)
        
        # Load and apply theme
        theme = self.settings.value("theme", "light", type=str)
        self.apply_theme(theme)

    def save_settings(self):
        """Save user settings to QSettings"""
        # Save current serial port
        current_port = self.port_combo.currentData()
        if current_port:
            self.settings.setValue("serial_port", current_port)

        # Save other settings
        self.settings.setValue("baudrate", self.baudrate_spin.value())
        self.settings.setValue("slave_id", self.slave_id_spin.value())
        self.settings.setValue("csv_logging", self.logging_checkbox.isChecked())
        
        # Save threshold settings
        self.settings.setValue("cell_delta_threshold", self.settings_widget.get_cell_delta_threshold())
        self.settings.setValue("pack_volt_min", self.settings_widget.get_pack_volt_min())
        self.settings.setValue("pack_volt_max", self.settings_widget.get_pack_volt_max())
        self.settings.setValue("theme", self.settings_widget.get_current_theme())

    def refresh_ports(self):
        """Refresh the list of available serial ports"""
        # Remember currently selected port
        current_port = self.port_combo.currentData() if hasattr(self, 'port_combo') else None

        self.port_combo.clear()
        ports = list(serial.tools.list_ports.comports())
        for port in ports:
            self.port_combo.addItem(f"{port.device} - {port.description}", port.device)

        # Restore previously selected port if it's still available
        if current_port:
            index = self.port_combo.findData(current_port)
            if index >= 0:
                self.port_combo.setCurrentIndex(index)
    
    def choose_csv_file(self):
        """Choose CSV file for logging"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Choose CSV File", 
            f"modbus_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            "CSV Files (*.csv)"
        )
        if filename:
            self.csv_filename = filename
            self.status_bar.showMessage(f"CSV file: {os.path.basename(filename)}")
    
    def start_monitoring(self):
        """Start monitoring battery data"""
        if self.port_combo.currentData() is None:
            QMessageBox.warning(self, "Warning", "No serial port selected!")
            return
        
        port = self.port_combo.currentData()
        baudrate = self.baudrate_spin.value()
        slave_id = self.slave_id_spin.value()
        
        # Configure worker
        self.worker.configure(port, baudrate, 'E', slave_id, 1.0)

        # Save current settings
        self.save_settings()

        # Always create a new log file with timestamp when starting monitoring
        os.makedirs("log", exist_ok=True)
        self.csv_filename = f"log/modbus_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        self.status_bar.showMessage(f"CSV logging to: {os.path.basename(self.csv_filename)}")

        # Start session tracking if available
        if self.session_manager:
            connection_params = {
                'port': port,
                'baudrate': baudrate,
                'slave_id': slave_id,
                'parity': 'E',
                'csv_filename': self.csv_filename
            }
            session_id = self.session_manager.start_manual_session(connection_params)
            self.session_manager.associate_csv_file(os.path.basename(self.csv_filename))

        # Start monitoring
        self.worker.start_monitoring()
        
        # Update UI
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.port_combo.setEnabled(False)
        self.baudrate_spin.setEnabled(False)
        self.slave_id_spin.setEnabled(False)
    
    def stop_monitoring(self):
        """Stop monitoring battery data"""
        if self.worker:
            self.worker.stop_monitoring()
        
        # End session if available
        if self.session_manager:
            self.session_manager.end_session()
        
        # Update UI
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.port_combo.setEnabled(True)
        self.baudrate_spin.setEnabled(True)
        self.slave_id_spin.setEnabled(True)
        

        self.status_bar.showMessage("Stopped")
    
    def handle_new_data(self, data: Dict[str, float]):
        """Handle new data from Modbus worker"""
        timestamp = data.get('timestamp', datetime.now())
        
        # Check for auto-start conditions if session manager is available
        if self.session_manager and 'afe_current' in data:
            current_value = data['afe_current']
            connection_params = {
                'port': self.port_combo.currentData(),
                'baudrate': self.baudrate_spin.value(),
                'slave_id': self.slave_id_spin.value(),
                'parity': 'E',
                'csv_filename': self.csv_filename
            }
            auto_session_id = self.session_manager.check_auto_start_conditions(current_value, connection_params)
            if auto_session_id:
                # Auto-session started, associate CSV file
                self.session_manager.associate_csv_file(os.path.basename(self.csv_filename))
        
        # Update session data if active
        if self.session_manager and self.session_manager.current_session:
            # Extract cell voltages for session tracking
            cell_voltages = []
            for i in range(1, 9):
                param = f"afe_cell_volt{i}"
                if param in data:
                    cell_voltages.append(data[param])
            
            session_data = {
                'cell_voltages': cell_voltages,
                'pack_voltage': data.get('afe_pack_volt'),
                'current': data.get('afe_current'),
                'timestamp': timestamp
            }
            self.session_manager.update_session_data(session_data)
        
        # Update cell voltage plot
        for i in range(1, 9):  # 8 cells
            param = f"afe_cell_volt{i}"
            if param in data:
                self.cell_voltage_plot.update_data(f"Cell {i}", data[param], timestamp)
        
        # Update pack voltage and current plot
        if 'afe_pack_volt' in data:
            self.pack_plot.update_data("Pack Voltage (V)", data['afe_pack_volt'], timestamp)
        if 'afe_current' in data:
            self.pack_plot.update_data("Current (A)", data['afe_current'], timestamp)
        
        # Update data table
        self.data_table.update_data(data)
        
        # Update CSV log table
        self.csv_log_table.update_data(data)
        
        # Always log to CSV file
        try:
            write_modbus_data_to_csv(data, self.csv_filename)
        except Exception as e:
            print(f"CSV logging error: {e}")
            self.status_bar.showMessage(f"CSV logging error: {e}")
        
        # Update status
        self.status_bar.showMessage(f"Last update: {timestamp.strftime('%H:%M:%S')}")
    
    def handle_status_change(self, status: str):
        """Handle status change from worker"""
        self.status_bar.showMessage(f"Status: {status}")
        
        # Update commands widget connection status
        if hasattr(self, 'commands_widget'):
            connected = status == "Connected"
            self.commands_widget.update_connection_status(connected)
    
    def handle_error(self, error: str):
        """Handle error from worker"""
        self.status_bar.showMessage(f"Error: {error}")
        QMessageBox.warning(self, "Communication Error", error)
    
    def clear_csv_log(self):
        """Clear the CSV log table"""
        self.csv_log_table.clear_data()
        self.status_bar.showMessage("CSV log cleared")
    
    def on_session_started(self, session_id: str, trigger_type: str):
        """Handle session started signal"""
        if trigger_type == 'manual':
            self.status_bar.showMessage(f"Session started: {session_id[:8]}...")
        else:
            self.status_bar.showMessage(f"Auto-session started ({trigger_type}): {session_id[:8]}...")
    
    def on_session_ended(self, session_id: str, session_summary: dict):
        """Handle session ended signal"""
        duration = session_summary.get('duration_seconds', 0)
        record_count = session_summary.get('data_summary', {}).get('total_records', 0)
        self.status_bar.showMessage(f"Session ended: {duration}s, {record_count} records")
    
    def on_auto_start_triggered(self, trigger_type: str, current_value: float):
        """Handle auto-start triggered signal"""
        self.status_bar.showMessage(f"Auto-start triggered: {trigger_type} at {current_value:.2f}A")
    
    def closeEvent(self, event):
        """Handle window close event"""
        if self.worker and self.worker.running:
            self.stop_monitoring()

        # Save settings before closing
        self.save_settings()
        event.accept()
    
    def apply_theme(self, theme: str):
        """Apply theme to all components"""
        # Apply to plot widgets
        self.cell_voltage_plot.apply_theme(theme)
        self.pack_plot.apply_theme(theme)
        
        # Apply theme to the CSV viewer plot as well
        if hasattr(self.csv_viewer_widget, 'csv_plot'):
            self.csv_viewer_widget.csv_plot.apply_theme(theme)
        
        # Apply application-wide stylesheet
        if theme == "dark":
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QGroupBox {
                    background-color: #3c3c3c;
                    border: 1px solid #555555;
                    border-radius: 5px;
                    margin: 3px;
                    padding-top: 15px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                    color: #ffffff;
                }
                QTableWidget {
                    background-color: #3c3c3c;
                    gridline-color: #555555;
                    color: #ffffff;
                }
                QTableWidget::item {
                    background-color: #3c3c3c;
                    color: #ffffff;
                }
                QTableWidget::item:selected {
                    background-color: #4a90e2;
                }
                QHeaderView::section {
                    background-color: #2b2b2b;
                    color: #ffffff;
                    border: 1px solid #555555;
                }
                QComboBox, QSpinBox, QDoubleSpinBox {
                    background-color: #3c3c3c;
                    color: #ffffff;
                    border: 1px solid #555555;
                    border-radius: 3px;
                    padding: 2px;
                }
                QPushButton {
                    background-color: #4a90e2;
                    color: #ffffff;
                    border: none;
                    border-radius: 3px;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: #5ba0f2;
                }
                QPushButton:pressed {
                    background-color: #3a80d2;
                }
                QLabel {
                    color: #ffffff;
                }
                QCheckBox {
                    color: #ffffff;
                }
                QTabWidget::pane {
                    border: 1px solid #555555;
                    background-color: #2b2b2b;
                }
                QTabBar::tab {
                    background-color: #3c3c3c;
                    color: #ffffff;
                    border: 1px solid #555555;
                    padding: 5px 10px;
                }
                QTabBar::tab:selected {
                    background-color: #4a90e2;
                }
                QTextEdit {
                    background-color: #3c3c3c;
                    color: #ffffff;
                    border: 1px solid #555555;
                }
                QListWidget {
                    background-color: #3c3c3c;
                    color: #ffffff;
                    border: 1px solid #555555;
                }
                QListWidget::item:selected {
                    background-color: #4a90e2;
                }
            """)
        elif theme == "light":
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #ffffff;
                    color: #000000;
                }
                QGroupBox {
                    background-color: #f5f5f5;
                    border: 1px solid #cccccc;
                    border-radius: 5px;
                    margin: 3px;
                    padding-top: 15px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                    color: #000000;
                }
                QTableWidget {
                    background-color: #ffffff;
                    gridline-color: #cccccc;
                    color: #000000;
                }
                QTableWidget::item {
                    background-color: #ffffff;
                    color: #000000;
                }
                QTableWidget::item:selected {
                    background-color: #4a90e2;
                    color: #ffffff;
                }
                QHeaderView::section {
                    background-color: #f5f5f5;
                    color: #000000;
                    border: 1px solid #cccccc;
                }
                QComboBox, QSpinBox, QDoubleSpinBox {
                    background-color: #ffffff;
                    color: #000000;
                    border: 1px solid #cccccc;
                    border-radius: 3px;
                    padding: 2px;
                }
                QPushButton {
                    background-color: #4a90e2;
                    color: #ffffff;
                    border: none;
                    border-radius: 3px;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: #5ba0f2;
                }
                QPushButton:pressed {
                    background-color: #3a80d2;
                }
                QLabel {
                    color: #000000;
                }
                QCheckBox {
                    color: #000000;
                }
                QTabWidget::pane {
                    border: 1px solid #cccccc;
                    background-color: #ffffff;
                }
                QTabBar::tab {
                    background-color: #f5f5f5;
                    color: #000000;
                    border: 1px solid #cccccc;
                    padding: 5px 10px;
                }
                QTabBar::tab:selected {
                    background-color: #4a90e2;
                    color: #ffffff;
                }
                QTextEdit {
                    background-color: #ffffff;
                    color: #000000;
                    border: 1px solid #cccccc;
                }
                QListWidget {
                    background-color: #ffffff;
                    color: #000000;
                    border: 1px solid #cccccc;
                }
                QListWidget::item:selected {
                    background-color: #4a90e2;
                    color: #ffffff;
                }
            """)
        else:  # system theme
            self.setStyleSheet("")  # Use system default


def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("GA BMS Monitor")
    app.setApplicationVersion(BMSMainWindow.APP_VERSION)
    
    # Create and show main window
    window = BMSMainWindow()
    window.show()
    
    # Start event loop
    sys.exit(app.exec())


if __name__ == "__main__":
    main()