"""
Theming and styling module for GA BMS Monitor application
"""

def get_dark_theme_style():
    """Returns the dark theme stylesheet"""
    return """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QGroupBox {
            background-color: #3c3c3c;
            border: 1px solid #555555;
            border-radius: 5px;
            margin: 3px;
            padding-top: 15px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #ffffff;
        }
        QTableWidget {
            background-color: #3c3c3c;
            gridline-color: #555555;
            color: #ffffff;
        }
        QTableWidget::item {
            background-color: #3c3c3c;
            color: #ffffff;
        }
        QTableWidget::item:selected {
            background-color: #4a90e2;
        }
        QHeaderView::section {
            background-color: #2b2b2b;
            color: #ffffff;
            border: 1px solid #555555;
        }
        QComboBox, Q<PERSON><PERSON><PERSON><PERSON>, QDouble<PERSON>pinBox {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 3px;
            padding: 2px;
        }
        QPushButton {
            background-color: #4a90e2;
            color: #ffffff;
            border: none;
            border-radius: 3px;
            padding: 5px;
        }
        QPushButton:hover {
            background-color: #5ba0f2;
        }
        QPushButton:pressed {
            background-color: #3a80d2;
        }
        QLabel {
            color: #ffffff;
        }
        QCheckBox {
            color: #ffffff;
        }
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #2b2b2b;
        }
        QTabBar::tab {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #555555;
            padding: 5px 10px;
        }
        QTabBar::tab:selected {
            background-color: #4a90e2;
        }
        QTextEdit {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #555555;
        }
        QListWidget {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #555555;
        }
        QListWidget::item:selected {
            background-color: #4a90e2;
        }
    """


def get_light_theme_style():
    """Returns the light theme stylesheet"""
    return """
        QMainWindow {
            background-color: #ffffff;
            color: #000000;
        }
        QGroupBox {
            background-color: #f5f5f5;
            border: 1px solid #cccccc;
            border-radius: 5px;
            margin: 3px;
            padding-top: 15px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #000000;
        }
        QTableWidget {
            background-color: #ffffff;
            gridline-color: #cccccc;
            color: #000000;
        }
        QTableWidget::item {
            background-color: #ffffff;
            color: #000000;
        }
        QTableWidget::item:selected {
            background-color: #4a90e2;
            color: #ffffff;
        }
        QHeaderView::section {
            background-color: #f5f5f5;
            color: #000000;
            border: 1px solid #cccccc;
        }
        QComboBox, QSpinBox, QDoubleSpinBox {
            background-color: #ffffff;
            color: #000000;
            border: 1px solid #cccccc;
            border-radius: 3px;
            padding: 2px;
        }
        QPushButton {
            background-color: #4a90e2;
            color: #ffffff;
            border: none;
            border-radius: 3px;
            padding: 5px;
        }
        QPushButton:hover {
            background-color: #5ba0f2;
        }
        QPushButton:pressed {
            background-color: #3a80d2;
        }
        QLabel {
            color: #000000;
        }
        QCheckBox {
            color: #000000;
        }
        QTabWidget::pane {
            border: 1px solid #cccccc;
            background-color: #ffffff;
        }
        QTabBar::tab {
            background-color: #f5f5f5;
            color: #000000;
            border: 1px solid #cccccc;
            padding: 5px 10px;
        }
        QTabBar::tab:selected {
            background-color: #4a90e2;
            color: #ffffff;
        }
        QTextEdit {
            background-color: #ffffff;
            color: #000000;
            border: 1px solid #cccccc;
        }
        QListWidget {
            background-color: #ffffff;
            color: #000000;
            border: 1px solid #cccccc;
        }
        QListWidget::item:selected {
            background-color: #4a90e2;
            color: #ffffff;
        }
    """


def get_plot_theme(theme: str) -> dict:
    """
    Returns plot styling configuration for PyQtGraph
    
    Args:
        theme: "dark", "light", or "system"
        
    Returns:
        Dictionary with plot styling parameters
    """
    if theme == "dark":
        return {
            'background': '#2b2b2b',
            'foreground': '#ffffff',
            'grid_color': '#555555',
            'grid_alpha': 100
        }
    elif theme == "light":
        return {
            'background': '#ffffff',
            'foreground': '#000000', 
            'grid_color': '#cccccc',
            'grid_alpha': 100
        }
    else:  # system
        return {
            'background': None,
            'foreground': None,
            'grid_color': '#808080',
            'grid_alpha': 100
        }


def apply_theme_to_widget(widget, theme: str):
    """
    Apply theme to a widget
    
    Args:
        widget: The QWidget to apply the theme to
        theme: "dark", "light", or "system"
    """
    if theme == "dark":
        widget.setStyleSheet(get_dark_theme_style())
    elif theme == "light":
        widget.setStyleSheet(get_light_theme_style())
    else:  # system theme
        widget.setStyleSheet("")  # Use system default