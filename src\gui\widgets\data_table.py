"""
Data table widget for displaying current battery parameter values
"""

from typing import Dict
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem
from PyQt6.QtGui import QColor


class DataTableWidget(QWidget):
    """Widget for displaying current data values in a table"""

    # Threshold for cell voltage delta in mV (adjust as needed)
    CELL_DELTA_THRESHOLD_MV = 50.0

    def __init__(self):
        super().__init__()
        self.data_rows = {}
        self.settings_widget = None  # Will be set by main window
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the table widget UI"""
        layout = QVBoxLayout()
        
        # Create table
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(['Parameter', 'Value', 'Unit'])
        self.table.horizontalHeader().setStretchLastSection(True)
        
        layout.addWidget(self.table)
        self.setLayout(layout)
    
    def set_settings_widget(self, settings_widget):
        """Set reference to settings widget for threshold access"""
        self.settings_widget = settings_widget
    
    def calculate_cell_voltage_delta(self, data: Dict[str, float]) -> float:
        """Calculate the delta between highest and lowest cell voltages in mV"""
        cell_voltages = []
        for i in range(1, 9):  # Cells 1-8
            param = f"afe_cell_volt{i}"
            if param in data:
                cell_voltages.append(data[param])
        
        if len(cell_voltages) >= 2:
            return (max(cell_voltages) - min(cell_voltages)) * 1000  # Convert V to mV
        return 0.0
    
    def get_color_for_value(self, param: str, value: float, data: Dict[str, float]) -> QColor:
        """Determine color for a parameter value based on thresholds"""

        # Cell voltage delta color logic (independent of settings widget)
        if param == 'afe_cell_volt_delta':
            if value > self.CELL_DELTA_THRESHOLD_MV:
                return QColor(255, 0, 0)  # Red for exceeding threshold
            else:
                return QColor(0, 128, 0)  # Green for within threshold

        # For other parameters, check if settings widget is available
        if not self.settings_widget:
            return QColor(0, 0, 0)  # Default black

        # Cell voltage color logic
        if 'afe_cell_volt' in param and param != 'afe_cell_volt_delta':
            cell_delta = self.calculate_cell_voltage_delta(data)
            delta_threshold = self.settings_widget.get_cell_delta_threshold()

            if cell_delta > delta_threshold:
                return QColor(255, 0, 0)  # Red for warning
            else:
                return QColor(0, 128, 0)  # Green for normal

        # Pack voltage color logic
        elif param == 'afe_pack_volt':
            min_threshold = self.settings_widget.get_pack_volt_min()
            max_threshold = self.settings_widget.get_pack_volt_max()

            if value < min_threshold or value > max_threshold:
                return QColor(255, 0, 0)  # Red for out of range
            else:
                return QColor(0, 128, 0)  # Green for in range

        # Default color for other parameters
        return QColor(0, 0, 0)  # Black
    
    def update_data(self, data: Dict[str, float]):
        """Update table with new data"""
        current_row = 0
        
        for param, value in data.items():
            if param == 'timestamp':
                continue
                
            # Determine unit based on parameter name
            if 'volt' in param:
                unit = 'V'
                formatted_value = f"{value:.3f}"
            elif 'current' in param:
                unit = 'A' 
                formatted_value = f"{value:.3f}"
            elif 'temp' in param:
                unit = '°C'
                formatted_value = f"{value:.1f}"
            elif 'charge' in param:
                unit = '%'
                formatted_value = f"{value:.1f}"
            elif 'capacity' in param:
                unit = 'Ah'
                formatted_value = f"{value:.2f}"
            elif 'cycle' in param:
                unit = 'cycles'
                formatted_value = f"{int(value)}"
            else:
                unit = ''
                formatted_value = f"{value}"
            
            # Add or update row
            if param not in self.data_rows:
                self.table.setRowCount(current_row + 1)
                self.data_rows[param] = current_row
                
                # Set parameter name
                param_item = QTableWidgetItem(param.replace('_', ' ').title())
                self.table.setItem(current_row, 0, param_item)
                
                # Set unit
                unit_item = QTableWidgetItem(unit)
                self.table.setItem(current_row, 2, unit_item)
                
                current_row += 1
            
            # Update value with color coding
            row = self.data_rows[param]
            value_item = QTableWidgetItem(formatted_value)
            
            # Apply color based on thresholds
            color = self.get_color_for_value(param, value, data)
            value_item.setForeground(color)
            
            self.table.setItem(row, 1, value_item)