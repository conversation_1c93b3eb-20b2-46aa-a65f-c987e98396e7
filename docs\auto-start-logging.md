
# Auto Start Logging

- Auto Start logging is a feature that allows csv logging to start automatically based on user defined thresholds based on current.

- Auto start logging can only be activated when the user has started a new session or has loaded a session and is connected to the battery bms via serial 

- auto_

## Thresholds

- auto-log-charging-threshold:
    - value: float
    - function: when current is >= auto-log-charging-threshold, the app will automatically start logging to csv file, and the session is considered active.
- auto-log-discharging-threshold:
    - value: float
    - function: when current is <= auto-log-discharging-threshold, the app will automatically start logging to csv file, and the session is considered active.


### Configuration

    - Settings:
        - Add auto log threshold settings to the settings page.
        - Add a dropmenu for auto log mode

# Auto log settings

- auto_log_charge_enable:
- auto_log_charge_end_time:

- auto_log_discharge_enable:
- auto_log_discharge_end_time:


## Auto log mode

- OFF: Auto logging is disabled
- ENABLE: Auto logging is enabled.


# Edge Cases

- Pack Recovery:
    - Some use cases include capturing BMS data, even after current flow has been cut off, and the charge or discharge event has been completed. 
    - Such as logging the pack's recovery, when discharging has completed, and the OCV pack voltage slowing rises.