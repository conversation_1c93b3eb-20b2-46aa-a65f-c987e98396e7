@echo off
echo Starting GA Battery Management System...
echo.

REM Change to the script directory
cd /d "%~dp0"

REM Use PowerShell to handle the Unix-style virtual environment properly
echo Launching with virtual environment...

REM Check if src/app.py exists
if not exist "src\app.py" (
    echo ERROR: src\app.py not found!
    echo Please make sure src\app.py is in the current directory.
    pause
    exit /b 1
)

REM Launch using PowerShell to handle Unix-style venv properly
echo Launching GA Battery Management System GUI...
echo.

powershell -ExecutionPolicy Bypass -Command "& { $env:VIRTUAL_ENV = (Get-Location).Path + '\venv'; $env:PATH = $env:VIRTUAL_ENV + '\bin;' + $env:PATH; .\venv\bin\python -m src.app }"

REM Check if the app ran successfully
if errorlevel 1 (
    echo.
    echo ERROR: Application failed to start!
    echo Trying alternative method...
    echo.

    REM Fallback: try with system Python and hope packages are installed
    python -m src.app

    if errorlevel 1 (
        echo.
        echo ERROR: Both methods failed!
        echo Please check that:
        echo 1. Virtual environment is properly set up
        echo 2. Required packages are installed
        echo 3. Python is accessible
        pause
    )
) else (
    echo.
    echo Application closed successfully.
)

echo.
echo Press any key to exit...
pause >nul
