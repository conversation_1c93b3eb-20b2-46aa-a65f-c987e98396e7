"""
Modbus communication worker thread for GA BMS Monitor
"""

from datetime import datetime
from PyQt6.QtCore import QThread, pyqtSignal
from pymodbus.client import ModbusSerialClient
from pymodbus.exceptions import ModbusException

from ..utils.constants import (
    REGISTER_MAP, DEFAULT_BAUDRATE, DEFAULT_PARITY, DEFAULT_SLAVE_ID,
    DEFAULT_INTERVAL, scale_voltage, scale_current, scale_temperature
)


class ModbusWorker(QThread):
    """Worker thread for Modbus communication to prevent GUI blocking"""
    
    dataReceived = pyqtSignal(dict)
    statusChanged = pyqtSignal(str)
    errorOccurred = pyqtSignal(str)
    
    def __init__(self, session_manager=None):
        super().__init__()
        self.client = None
        self.running = False
        self.port = None
        self.baudrate = DEFAULT_BAUDRATE
        self.parity = DEFAULT_PARITY
        self.slave_id = DEFAULT_SLAVE_ID
        self.interval = DEFAULT_INTERVAL
        self.session_manager = session_manager
        self.register_map = REGISTER_MAP
    
    def configure(self, port: str, baudrate: int = DEFAULT_BAUDRATE, 
                  parity: str = DEFAULT_PARITY, slave_id: int = 2, 
                  interval: float = DEFAULT_INTERVAL):
        """Configure Modbus connection parameters"""
        self.port = port
        self.baudrate = baudrate
        self.parity = parity
        self.slave_id = slave_id
        self.interval = interval
    
    def start_monitoring(self):
        """Start the monitoring thread"""
        self.running = True
        self.start()
    
    def stop_monitoring(self):
        """Stop the monitoring thread"""
        self.running = False
        if self.client and self.client.connected:
            self.client.close()
        self.wait()
    
    def run(self):
        """Main thread execution"""
        try:
            self.client = ModbusSerialClient(
                port=self.port,
                baudrate=self.baudrate,
                parity=self.parity,
                stopbits=1,
                bytesize=8,
                timeout=2,
                retries=3
            )
            
            if not self.client.connect():
                self.errorOccurred.emit(f"Failed to connect to {self.port}")
                return
            
            self.statusChanged.emit("Connected")
            
            while self.running:
                try:
                    # Read registers (start at address 9, read 30 registers)
                    response = self.client.read_input_registers(
                        address=9,
                        count=30,
                        slave=self.slave_id
                    )
                    
                    if not response.isError():
                        # Parse register values
                        values = response.registers
                        mapped_data = {}
                        
                        for i, value in enumerate(values):
                            register_address = 10 + i
                            if register_address in self.register_map:
                                param_name = self.register_map[register_address]
                                
                                # Apply scaling based on parameter type
                                if 'volt' in param_name and 'cell' in param_name:
                                    # Cell voltages in mV, convert to V
                                    scaled_value = scale_voltage(value)
                                elif param_name == 'afe_pack_volt':
                                    # Pack voltage in mV, convert to V  
                                    scaled_value = scale_voltage(value)
                                elif 'current' in param_name:
                                    # Current in mA, convert to A
                                    scaled_value = scale_current(value)
                                elif 'temp' in param_name:
                                    # Temperature conversion
                                    scaled_value = scale_temperature(value)
                                else:
                                    scaled_value = value
                                
                                mapped_data[param_name] = scaled_value
                        
                        # Add timestamp
                        mapped_data['timestamp'] = datetime.now()
                        
                        self.dataReceived.emit(mapped_data)
                        
                    else:
                        self.errorOccurred.emit(f"Modbus read error: {response}")
                        
                    self.msleep(int(self.interval * 1000))
                    
                except ModbusException as e:
                    self.errorOccurred.emit(f"Modbus exception: {e}")
                    self.msleep(5000)  # Wait 5 seconds before retry
                except Exception as e:
                    self.errorOccurred.emit(f"Unexpected error: {e}")
                    self.msleep(5000)
                    
        except Exception as e:
            self.errorOccurred.emit(f"Connection error: {e}")
        finally:
            if self.client and self.client.connected:
                self.client.close()
            self.statusChanged.emit("Disconnected")