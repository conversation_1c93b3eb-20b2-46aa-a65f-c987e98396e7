"""
Unit tests for ModbusWorker functionality.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from src.core.modbus_worker import ModbusWorker


class TestModbusWorker:
    """Test cases for the ModbusWorker class."""
    
    def test_modbus_worker_initialization(self):
        """Test that ModbusWorker initializes with correct default values."""
        worker = ModbusWorker()
        
        assert worker.running is False
        assert worker.port is None
        assert worker.baudrate == 9600
        assert worker.parity == 'E'
        assert worker.slave_id == 2
        assert worker.interval == 1.0
        assert worker.client is None
        assert len(worker.register_map) > 0
    
    def test_configure_modbus_parameters(self):
        """Test that configure() properly sets Modbus parameters."""
        worker = ModbusWorker()
        
        worker.configure(
            port="/dev/ttyUSB0",
            baudrate=19200,
            parity='N',
            slave_id=1,
            interval=0.5
        )
        
        assert worker.port == "/dev/ttyUSB0"
        assert worker.baudrate == 19200
        assert worker.parity == 'N'
        assert worker.slave_id == 1
        assert worker.interval == 0.5
    
    def test_configure_partial_parameters(self):
        """Test that configure() with partial parameters keeps defaults for others."""
        worker = ModbusWorker()
        original_baudrate = worker.baudrate
        original_parity = worker.parity
        
        worker.configure(port="/dev/ttyUSB0", slave_id=3)
        
        assert worker.port == "/dev/ttyUSB0"
        assert worker.slave_id == 3
        # Should keep original values for unspecified parameters
        assert worker.baudrate == original_baudrate
        assert worker.parity == original_parity
    
    def test_process_register_data_cell_voltages(self):
        """Test that _process_register_data correctly scales cell voltage values."""
        worker = ModbusWorker()
        
        # Mock register values (in mV)
        raw_values = [
            3456,  # afe_cell_volt1: 3456 mV -> 3.456 V
            3457,  # afe_cell_volt2: 3457 mV -> 3.457 V
            3455,  # afe_cell_volt3: 3455 mV -> 3.455 V
            # ... continue for other registers
        ] + [0] * 27  # Fill remaining 27 registers with zeros
        
        result = worker._process_register_data(raw_values)
        
        # Test that cell voltages are properly scaled from mV to V
        assert result['afe_cell_volt1'] == 3.456
        assert result['afe_cell_volt2'] == 3.457
        assert result['afe_cell_volt3'] == 3.455
    
    def test_process_register_data_pack_voltage(self):
        """Test that pack voltage is correctly scaled."""
        worker = ModbusWorker()
        
        # Create mock data with pack voltage at register 18 (index 8)
        raw_values = [0] * 8 + [27652] + [0] * 21  # 27652 mV -> 27.652 V
        
        result = worker._process_register_data(raw_values)
        
        assert result['afe_pack_volt'] == 27.652
    
    def test_process_register_data_current(self):
        """Test that current values are correctly scaled."""
        worker = ModbusWorker()
        
        # Create mock data with current at register 22 (index 12)
        raw_values = [0] * 12 + [2345] + [0] * 17  # 2345 mA -> 2.345 A
        
        result = worker._process_register_data(raw_values)
        
        assert result['afe_current'] == 2.345
    
    def test_process_register_data_temperature(self):
        """Test that temperature values are correctly scaled."""
        worker = ModbusWorker()
        
        # Create mock data with temperature at register 20 (index 10)
        raw_values = [0] * 10 + [255] + [0] * 19  # 255 -> 25.5 °C
        
        result = worker._process_register_data(raw_values)
        
        assert result['afe_temp1'] == 25.5
    
    def test_start_monitoring(self):
        """Test that start_monitoring sets running flag and starts thread."""
        worker = ModbusWorker()
        
        with patch.object(worker, 'start') as mock_start:
            worker.start_monitoring()
            
            assert worker.running is True
            mock_start.assert_called_once()
    
    def test_stop_monitoring_with_connected_client(self):
        """Test that stop_monitoring properly closes connected client."""
        worker = ModbusWorker()
        worker.running = True
        
        # Mock connected client
        mock_client = Mock()
        mock_client.connected = True
        worker.client = mock_client
        
        with patch.object(worker, 'wait') as mock_wait:
            worker.stop_monitoring()
            
            assert worker.running is False
            mock_client.close.assert_called_once()
            mock_wait.assert_called_once()
    
    def test_stop_monitoring_without_client(self):
        """Test that stop_monitoring works when no client is connected."""
        worker = ModbusWorker()
        worker.running = True
        worker.client = None
        
        with patch.object(worker, 'wait') as mock_wait:
            worker.stop_monitoring()
            
            assert worker.running is False
            mock_wait.assert_called_once()
    
    @patch('src.core.modbus_worker.ModbusSerialClient')
    def test_run_successful_connection(self, mock_client_class):
        """Test successful Modbus connection and data reading."""
        worker = ModbusWorker()
        worker.port = "/dev/ttyUSB0"
        worker.running = True
        
        # Mock client instance
        mock_client = Mock()
        mock_client.connect.return_value = True
        mock_client.connected = True
        mock_client_class.return_value = mock_client
        
        # Mock successful register reading
        mock_response = Mock()
        mock_response.isError.return_value = False
        mock_response.registers = [3456, 3457] + [0] * 28  # Mock register values
        mock_client.read_input_registers.return_value = mock_response
        
        # Mock signals
        worker.statusChanged = Mock()
        worker.dataReceived = Mock()
        worker.errorOccurred = Mock()
        
        # Stop after first iteration
        def stop_after_first():
            worker.running = False
        worker.msleep = Mock(side_effect=stop_after_first)
        
        worker.run()
        
        # Verify connection attempt
        mock_client.connect.assert_called_once()
        worker.statusChanged.emit.assert_called_with("Connected")
        
        # Verify data reading
        mock_client.read_input_registers.assert_called_with(
            address=9, count=30, slave=worker.slave_id
        )
        
        # Verify data was emitted
        worker.dataReceived.emit.assert_called_once()
        args = worker.dataReceived.emit.call_args[0]
        data = args[0]
        assert 'timestamp' in data
        assert isinstance(data['timestamp'], datetime)
    
    @patch('src.core.modbus_worker.ModbusSerialClient')
    def test_run_connection_failure(self, mock_client_class):
        """Test handling of connection failure."""
        worker = ModbusWorker()
        worker.port = "/dev/ttyUSB0"
        worker.running = True
        
        # Mock client that fails to connect
        mock_client = Mock()
        mock_client.connect.return_value = False
        mock_client_class.return_value = mock_client
        
        # Mock signals
        worker.errorOccurred = Mock()
        
        worker.run()
        
        # Verify error was emitted
        worker.errorOccurred.emit.assert_called_with("Failed to connect to /dev/ttyUSB0")