"""
Commands widget for sending firmware commands to the battery management system
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTextEdit, QComboBox, QSpinBox, QGroupBox, QFormLayout,
    QMessageBox
)


class CommandsWidget(QWidget):
    """Widget for firmware commands and diagnostics"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the commands UI"""
        layout = QVBoxLayout()
        
        # Header
        header = QLabel("<h2>⚡ Firmware Commands</h2>")
        layout.addWidget(header)
        
        # Command controls
        controls_group = QGroupBox("Command Controls")
        controls_layout = QFormLayout()
        
        # Command selection
        self.command_combo = QComboBox()
        self.command_combo.addItems([
            "Reset AFE",
            "Reset Fuel Gauge", 
            "Calibrate Current",
            "Read Device Info",
            "Read Diagnostics",
            "Factory Reset"
        ])
        controls_layout.addRow("Command:", self.command_combo)
        
        # Parameter input
        self.param_spin = QSpinBox()
        self.param_spin.setRange(0, 65535)
        controls_layout.addRow("Parameter:", self.param_spin)
        
        # Send button
        send_btn = QPushButton("Send Command")
        send_btn.clicked.connect(self.send_command)
        controls_layout.addRow(send_btn)
        
        controls_group.setLayout(controls_layout)
        layout.addWidget(controls_group)
        
        # Response display
        response_group = QGroupBox("Command Response")
        response_layout = QVBoxLayout()
        
        self.response_text = QTextEdit()
        self.response_text.setReadOnly(True)
        self.response_text.setMaximumHeight(200)
        response_layout.addWidget(self.response_text)
        
        clear_btn = QPushButton("Clear Log")
        clear_btn.clicked.connect(self.response_text.clear)
        response_layout.addWidget(clear_btn)
        
        response_group.setLayout(response_layout)
        layout.addWidget(response_group)
        
        # Warning
        warning = QLabel("⚠️ <b>Warning:</b> These commands directly interact with firmware. Use with caution.")
        warning.setStyleSheet("color: red; background-color: #fff3cd; padding: 10px; border: 1px solid #ffeaa7;")
        layout.addWidget(warning)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def send_command(self):
        """Send the selected command"""
        command = self.command_combo.currentText()
        param = self.param_spin.value()
        
        # For now, just log the command (would need Modbus integration)
        self.response_text.append(f"Command: {command}")
        self.response_text.append(f"Parameter: {param}")
        self.response_text.append("Status: Command functionality not yet implemented")
        self.response_text.append("=" * 40)
        
        QMessageBox.information(self, "Command", f"Command '{command}' queued for implementation")