# CONTEXT.md - Enhanced CSV Viewer & Changelog Implementation Context

## Project Overview
GA Modbus Python Application - Battery management system data logger with PyQt6 GUI, enhanced CSV viewer, and built-in changelog functionality.

## Recent Implementation: Enhanced CSV Viewer & Changelog Features (v1.1.0)

## Latest Update: Project Structure Refactoring

### Refactoring Tasks Completed
Reorganized project structure following Python packaging best practices:

1. **Src Directory Creation**: Moved all Python scripts to `src/` directory
2. **Package Structure**: Created proper Python package with `src/__init__.py`
3. **Import Updates**: Updated relative imports within the src package
4. **Launcher Scripts**: Updated all launcher scripts to use module execution
5. **Documentation Updates**: Updated CLAUDE.md and documentation files

### Tasks Completed
Implemented comprehensive enhancements to CSV viewer and added changelog functionality:

1. **Enhanced CSV Viewer**: Advanced directory navigation and data visualization
2. **Changelog Viewer Tab**: Built-in documentation and version tracking
3. **Improved File Browser**: Visual file/folder icons with intuitive navigation
4. **Better Column Detection**: Support for various CSV formats (Cell1_V, Pack_V, Current)
5. **Version Management**: Centralized version tracking (v1.1.0) across application
6. **Import Fix**: Resolved QListWidgetItem import error for proper startup

### Technical Details

#### File Structure
- `src/app.py` - Main PyQt6 application with enhanced CSVViewerWidget and new ChangelogViewerWidget
- `src/modbus_query_test.py` - Command-line Modbus data logger
- `src/ga_modbus_csv_writer.py` - CSV formatting and export functionality
- `src/check_ports.py` - Serial port detection utility
- `src/__init__.py` - Python package initialization
- `run_csv_viewer.sh` - Launcher script for easy startup
- `launch_app.bat`, `launch_app.ps1`, `run_app.bat` - Windows launcher scripts
- `venv/` - Virtual environment with all dependencies
- `CHANGELOG.md` - Version history with v1.1.0 release notes
- `docs/CONTEXT.md` - This context file

#### Key Implementation Components

**Enhanced CSVViewerWidget Class** (`src/app.py:531-967`):
- **Advanced Directory Navigation**: Breadcrumb controls, parent/log root buttons
- **Visual File Browser**: Folder 📁 and CSV 📄 icons with double-click navigation
- **Enhanced Column Detection**: Support for Cell1_V, Pack_V, Current column formats
- **Improved UI Layout**: Navigation controls, prioritized parameter dropdowns
- **Multi-series Plotting**: Comparative cell voltage analysis
- **Statistics Panel**: Battery-specific analysis (cell delta calculations)

**New ChangelogViewerWidget Class** (`src/app.py:969-1095`):
- **Markdown Rendering**: Converts CHANGELOG.md to styled HTML
- **Version Display**: Shows current application version (v1.1.0)
- **Auto-refresh**: Real-time changelog updates without restart
- **File Monitoring**: Displays last modification timestamp
- **Styled Display**: Professional formatting with headers, bold text, code highlighting

**Dependencies Added**:
- pandas - CSV data manipulation
- numpy - Numerical operations
- PyQt6 - GUI framework (already present)
- pyqtgraph - Plotting library (already present)

**Enhanced UI Layout**:

*CSV Viewer Tab*:
- Left Panel (300px max width):
  - **Breadcrumb Navigation**: Current directory path display
  - **Navigation Controls**: ↑ Parent and 🏠 Log Root buttons
  - **Visual File Browser**: Folder 📁 and CSV 📄 icons
  - **Directory/File List**: Double-click navigation support
  - Load button for selected files
- Right Panel:
  - **Chart Controls**: Y-axis selection with prioritized parameters
  - **Multi-series Options**: "Show all cell voltages" checkbox
  - **Interactive Charts**: Enhanced time series plotting
  - **Data Table**: Sortable, resizable columns with auto-sizing
  - **Statistics Panel**: Battery-specific analysis with cell voltage deltas

*Changelog Tab*:
- **Header**: Version display and refresh button
- **Content Area**: Styled HTML display of CHANGELOG.md
- **Footer**: File modification timestamp

#### Error Handling
- Graceful handling of missing pandas/numpy dependencies
- File reading error handling with user feedback
- Directory access error handling

#### Features Implemented

**Enhanced Directory Management**:
- **Battery Pack Navigation**: Browse specific pack folders (e.g., `log/0533/`, `log/0573/`)
- **Visual Indicators**: Folder 📁 and CSV 📄 icons for easy identification
- **Breadcrumb Navigation**: Clear directory path display
- **Quick Navigation**: Parent (↑) and Log Root (🏠) buttons
- **Double-click Navigation**: Intuitive folder/file interaction
- **Auto-creation**: Missing directories created automatically

**Advanced CSV Loading**:
- **Enhanced Column Detection**: Support for Cell1_V, Pack_V, Current formats
- **Parameter Prioritization**: Important battery parameters appear first in dropdowns
- **Robust Error Handling**: Comprehensive dependency and file access checks
- **Format Flexibility**: Works with various CSV naming conventions

**Enhanced Data Visualization**:
- **Multi-series Cell Analysis**: Comparative plotting of all cell voltages
- **Prioritized Parameters**: Pack voltage, current, SOC appear first
- **Intelligent Time Detection**: Supports timestamp, time, or index columns
- **Interactive Controls**: Enhanced plot customization options
- **Color Coordination**: Consistent color schemes across charts

**Advanced Statistics Analysis**:
- **Battery-specific Metrics**: Cell voltage balance analysis (mV deltas)
- **Comprehensive Statistics**: Mean, min, max, std dev for all numeric columns
- **Professional Display**: Styled HTML tables with proper formatting
- **Real-time Updates**: Statistics recalculate on data changes

**New Changelog Features**:
- **Markdown Rendering**: Styled HTML conversion with proper formatting
- **Version Tracking**: Current version (v1.1.0) display in window title
- **Live Updates**: Real-time changelog refresh without restart
- **File Monitoring**: Last modification timestamp display
- **Professional Styling**: Headers, bold text, code highlighting, links

### Virtual Environment Setup
```bash
python3 -m venv venv
./venv/bin/pip install pandas numpy pyqtgraph PyQt6 pymodbus pyserial tomli
```

### Launcher Scripts
- `run_csv_viewer.sh` - Unix/Linux launcher that uses virtual environment Python to launch `src.app` module
- `launch_app.bat`, `launch_app.ps1`, `run_app.bat` - Windows launchers updated for module execution
- All scripts updated to use `python -m src.app` for proper module execution

### Documentation Updates
Updated `CLAUDE.md` with:
- CSV Viewer feature description
- Usage instructions
- Requirements (pandas, numpy)
- Launcher script usage

### Recent Bug Fixes
- ✅ **QListWidgetItem Import Error**: Fixed missing import causing startup failure
- ✅ **Directory Navigation**: Resolved breadcrumb and navigation button functionality
- ✅ **Column Detection**: Enhanced CSV format compatibility for battery data

### Current Status (v1.1.0)
- ✅ Enhanced CSV viewer with directory navigation fully implemented
- ✅ Changelog viewer tab with markdown rendering functional
- ✅ Version tracking integrated across application (v1.1.0)
- ✅ Battery pack folder navigation working (log/0533/, log/0573/)
- ✅ Import errors resolved - application starts successfully
- ✅ All dependencies properly managed and documented
- ✅ CHANGELOG.md updated with comprehensive release notes

### Enhanced Usage
1. **Application Launch**: Run `./run_csv_viewer.sh` or `./venv/bin/python3 -m src.app`
2. **CSV Viewer Tab**: 
   - Navigate battery pack folders with visual icons
   - Use Parent (↑) and Log Root (🏠) buttons for quick navigation
   - Double-click folders to browse, CSV files to load
   - Select parameters from prioritized dropdown (Pack_V, Current, SOC first)
   - Enable "Show all cell voltages" for comparative analysis
3. **Changelog Tab**:
   - View version history with styled formatting
   - Use Refresh (🔄) button for real-time updates
   - Monitor file modification timestamps

### Updated Code Locations
- **Enhanced CSV Viewer**: `src/app.py:531-967`
- **Changelog Viewer**: `src/app.py:969-1095`
- **Main Window with Version**: `src/app.py:1201-1206` (BMSMainWindow.APP_VERSION)
- **Tab Integration**: `src/app.py:1298-1302` (includes Changelog tab)
- **Import Fixes**: `src/app.py:28-34` (QListWidgetItem added)
- **Modbus Logger**: `src/modbus_query_test.py` (command-line data logger)
- **CSV Writer**: `src/ga_modbus_csv_writer.py` (data formatting and export)
- **Version Documentation**: `CHANGELOG.md:10-63` (v1.1.0 release notes)

### Comprehensive Testing Status
- ✅ Application imports and launches without errors
- ✅ All 5 tabs functional (Real-time, CSV Log, CSV Viewer, Changelog, Settings)
- ✅ Directory navigation with battery pack folders working
- ✅ CSV loading with Cell1_V, Pack_V, Current formats supported
- ✅ Multi-series cell voltage plotting operational
- ✅ Changelog markdown rendering with proper styling
- ✅ Version tracking displays correctly (v1.1.0)
- ✅ Statistics panel with battery-specific analysis functional

### Architecture Enhancements
This implementation extends the original CSV viewer requirements with:
- **Battery Pack Organization**: Folder-based navigation for different battery packs
- **Professional Documentation**: Built-in changelog with version tracking  
- **Enhanced User Experience**: Visual indicators and intuitive navigation
- **Robust Error Handling**: Comprehensive dependency and file access management
- **Scalable Design**: Centralized version management for future releases

The enhanced implementation fully satisfies and exceeds the original requirements, providing a professional battery management application with comprehensive data analysis capabilities.